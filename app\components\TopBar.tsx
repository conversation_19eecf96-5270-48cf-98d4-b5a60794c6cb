"use client";

import { UserStage } from "./UserJourneyPage";
import ProgressBar from "./ProgressBar";
import LanguageSwitcher from "./LanguageSwitcher";

interface TopBarProps {
  currentStage?: UserStage;
  feedbackStep?: "initial" | "start" | "recording";
  hideLanguageSwitcher?: boolean;
  showProgressBar?: boolean;
  userLanguage: string;
}

/**
 * TopBar component that provides a consistent top section with logo, progress bar, and language switcher
 * Used across all screens in the user journey
 */
export default function TopBar({
  currentStage,
  feedbackStep,
  hideLanguageSwitcher = false,
  showProgressBar = true,
  userLanguage
}: TopBarProps) {
  return (
    <div className="fixed-top-bar bg-white">
      <div className="flex justify-between items-center w-full h-full px-3 xs:px-4 sm:px-6 md:px-8">
        {/* Logo - responsive size */}
        <div className="w-[36px] h-[36px] xs:w-[40px] xs:h-[40px] sm:w-[48px] sm:h-[48px] md:w-[56px] md:h-[56px] flex items-center">
          <img
            src="/images/logo_icon.png"
            alt="Lumalife icon"
            className="h-9 xs:h-10 sm:h-12 md:h-14 w-auto rounded-xl overflow-hidden"
          />
        </div>

        {/* Progress bar - responsive width and centered */}
        {showProgressBar && currentStage && (
          <div className="flex-1 mx-2 xs:mx-3 sm:mx-4 md:mx-5 flex justify-center items-center">
            <ProgressBar currentStage={currentStage} feedbackStep={feedbackStep} lng={userLanguage} />
          </div>
        )}

        {/* Language switcher positioned at the end */}
        <div className="flex items-center justify-end">
          {!hideLanguageSwitcher && (
            <LanguageSwitcher currentLanguage={userLanguage} />
          )}
        </div>
      </div>
    </div>
  );
}
