import AvatarSelectionPage from "../../components/AvatarSelectionPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Choose Your Assistant - Lu<PERSON><PERSON>",
    description: "Select your personal assistant avatar"
  };
}

// Server component that passes the lng parameter to the client component
export default async function AvatarSelection({
  params,
}: {
  params: { lng: string };
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <AvatarSelectionPage lng={lng} />;
}
