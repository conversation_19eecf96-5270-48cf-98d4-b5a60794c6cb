"use client";

import { useTranslation } from "../i18n/client";

// Component for final thank you screen
export default function FinalThankYouScreen({
  lng
}: {
  lng: string;
}) {
  // Use the t function from our client translations
  const { t } = useTranslation("common", lng);

  // No need for logout or reset journey functions
  // as they are handled by the UserActionButtons component

  return (
    <div className="text-center max-w-xs sm:max-w-sm md:max-w-md mx-auto relative px-2 sm:px-0 h-full flex flex-col justify-between">
      {/* Final thank you message */}
      <div className="mb-4 sm:mb-6 text-center pt-2 sm:pt-3 md:pt-4">
        <h2 className="text-xl sm:text-2xl md:text-2xl font-bold mb-4 sm:mb-5 md:mb-6 text-gray-800 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] bg-clip-text text-transparent">
          {t("completion_title")}
        </h2>

        <div className="space-y-4 sm:space-y-5 md:space-y-6 text-gray-700 px-1">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed">
            {t("completion_proud")}
          </p>

          <p className="text-sm sm:text-base md:text-lg leading-relaxed">
            {t("completion_feedback")}
          </p>

          <p className="text-sm sm:text-base md:text-lg leading-relaxed">
            {t("completion_journey")}
          </p>
        </div>
      </div>

      {/* Bottom section with signature and decorative elements */}
      <div className="mt-4 sm:mt-6 md:mt-8 pb-4 sm:pb-5 md:pb-6">
        <div className="w-16 h-0.5 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] mx-auto mb-4"></div>
        <p className="text-base sm:text-lg font-medium text-[#52bcc3]">
          {t("completion_team")}
        </p>
        <div className="flex justify-center items-center mt-3 space-x-2">
          <div className="w-2 h-2 rounded-full bg-[#52bcc3]/30"></div>
          <p className="text-xs text-gray-500">
            {t("completion_copyright")}
          </p>
          <div className="w-2 h-2 rounded-full bg-[#52bcc3]/30"></div>
        </div>
      </div>
    </div>
  );
}
