"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState, ReactNode } from "react";
import Link from "next/link";
import { useLanguage } from "../context/LanguageContext";
import { getTranslation } from "../i18n/translations";
import { getUserProfile, logout } from "../services/api";
import AdminUserManagement from "./AdminUserManagement";

// Embedded version of AdminUserManagement for the dashboard
const AdminUserManagementEmbed = ({ lng }: { lng: string }) => {
  return <AdminUserManagement lng={lng} embedded={true} />;
};

// Client component for the admin dashboard
export default function AdminDashboardPage({ lng }: { lng: string }) {
  const router = useRouter();
  const { currentLanguage } = useLanguage();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [userProfile, setUserProfile] = useState<any>(null);

  // Use the t function from our static translations
  const t = (key: string) => getTranslation(key, lng);

  // Check if user is authenticated and is an admin
  useEffect(() => {
    async function checkAuth() {
      try {
        const token = localStorage.getItem('token');
        const userRole = localStorage.getItem('userRole');

        if (!token) {
          console.log("No token found, redirecting to login page");
          // Redirect to login if not authenticated
          router.push(`/${lng}/login`);
          return;
        }

        if (userRole !== 'admin') {
          // Check if the email contains 'admin' as a fallback
          const userEmail = localStorage.getItem('userEmail');
          const isAdminEmail = userEmail && userEmail.toLowerCase().includes('admin');

          if (isAdminEmail) {
            localStorage.setItem('userRole', 'admin');
          } else {
            // Redirect to login if not an admin
            setError("You don't have permission to access the admin dashboard");
            setTimeout(() => {
              router.push(`/${lng}/login`);
            }, 2000);
            return;
          }
        }

        // Get user profile from API
        try {
          const profile = await getUserProfile();
          setUserProfile(profile);
          setIsLoading(false);
        } catch (profileErr) {
          console.error("Error fetching user profile:", profileErr);

          {
            // In all other cases, show error and redirect
            throw profileErr;
          }
        }
      } catch (err) {
        console.error("Error checking authentication:", err);
        setError("Authentication error. Please login again.");

        // Clear token and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userEmail');

        setTimeout(() => {
          router.push(`/${lng}/login`);
        }, 2000);
      }
    }

    checkAuth();
  }, [router, lng]);

  // Handle logout
  const handleLogout = async () => {
    try {
      // Clean up audio connections before logout
      if (typeof window !== 'undefined') {
        try {
          const audioUtils = await import('../utils/audioStreamingStatic');
          audioUtils.emergencyLogoutCleanup();
        } catch (cleanupErr) {
          console.log('Error during audio cleanup:', cleanupErr);
        }
      }

      // Always try to use the logout API
      await logout();
      router.push(`/${lng}/login`);
    } catch (err) {
      console.error("Error during logout:", err);
      // Force logout even if API call fails
      localStorage.removeItem('token');
      localStorage.removeItem('userRole');
      localStorage.removeItem('userEmail');
      router.push(`/${lng}/login`);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 max-w-md bg-white rounded-lg shadow-lg">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-700 mb-6">{error}</p>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  // Create a custom header for admin dashboard
  const adminHeader: ReactNode = (
    <div className="flex justify-between items-center h-[70px] py-2">
      <div className="flex items-center">
        <img
          src="/images/logo_full.png"
          alt="Lumalife"
          className="h-10 w-auto"
        />
        <span className="ml-4 text-lg font-semibold text-gray-800">
          {lng === "de" ? "Admin-Dashboard" : "Admin Dashboard"}
        </span>
      </div>
      <div className="flex items-center space-x-3">
        <button
          onClick={() => window.location.reload()}
          className="px-3 py-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center"
          title={lng === "de" ? "Daten aktualisieren" : "Refresh Data"}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {lng === "de" ? "Aktualisieren" : "Refresh"}
        </button>
        <button
          onClick={handleLogout}
          className="px-3 py-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          {lng === "de" ? "Abmelden" : "Logout"}
        </button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header */}
      <div className="bg-white shadow-sm w-full">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          {adminHeader}
        </div>
      </div>

      {/* Main Content - Simplified UI */}
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* User Management Section */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-gray-800">{lng === "de" ? "Benutzerverwaltung" : "User Management"}</h2>
          </div>

          {/* Embed the AdminUserManagement component directly */}
          <div className="w-full">
            <AdminUserManagementEmbed lng={lng} />
          </div>
        </div>
      </div>
    </div>
  );
}
