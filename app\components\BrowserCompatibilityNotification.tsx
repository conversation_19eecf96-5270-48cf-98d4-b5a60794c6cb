"use client";

import { useState, useEffect } from "react";
import { useBrowserCompatibility } from "../context/BrowserCompatibilityContext";

interface BrowserCompatibilityNotificationProps {
  position?: "top" | "bottom";
  showDismissButton?: boolean;
}

/**
 * Component to display browser compatibility notifications
 * This will show a notification when there are browser compatibility issues
 */
export default function BrowserCompatibilityNotification({
  position = "top",
  showDismissButton = true,
}: BrowserCompatibilityNotificationProps) {
  const { isBrowserCompatible, hasMicrophonePermission, error, clearError } = useBrowserCompatibility();
  const [isDismissed, setIsDismissed] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState<boolean>(false);

  // Only show notification when there's an error or critical compatibility issue
  // We don't show for microphone permission issues unless there's an explicit error
  useEffect(() => {
    if ((error || !isBrowserCompatible) && !isDismissed) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [error, isBrowserCompatible, isDismissed]);

  // Don't render anything if there's no error or compatibility issue
  if (!isVisible) return null;

  // Get the appropriate message
  const getMessage = () => {
    if (error) return error;
    if (!isBrowserCompatible) return "Your browser doesn't support all required features. Some functionality may be limited.";
    return "";
  };

  // Handle dismiss button click
  const handleDismiss = () => {
    setIsDismissed(true);
    clearError();
  };

  // Position styles
  const positionStyles = {
    top: "fixed top-0 left-0 right-0 z-50 mt-4",
    bottom: "fixed bottom-0 left-0 right-0 z-50 mb-4",
  };

  return (
    <div className={`${positionStyles[position]} px-4`}>
      <div className="max-w-md mx-auto p-4 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm text-yellow-700">{getMessage()}</p>
            <p className="mt-2 text-xs text-yellow-600">You can continue using the application, but some features may not work properly.</p>
          </div>
          {showDismissButton && (
            <div className="ml-4 flex-shrink-0">
              <button
                type="button"
                className="inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"
                onClick={handleDismiss}
              >
                <span className="sr-only">Close</span>
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
