"use client";

import { useState } from 'react';

interface ValidationRules {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  isEmail?: boolean;
  isPhone?: boolean;
  custom?: (value: string) => boolean;
}

interface ValidationErrors {
  [key: string]: string;
}

/**
 * Custom hook for form validation
 */
export function useFormValidation() {
  const [errors, setErrors] = useState<ValidationErrors>({});

  /**
   * Validates a single field
   * @param name Field name
   * @param value Field value
   * @param rules Validation rules
   * @param errorMessages Custom error messages
   * @returns True if valid, false otherwise
   */
  const validateField = (
    name: string,
    value: string,
    rules: ValidationRules,
    errorMessages: { [key: string]: string } = {}
  ): boolean => {
    let isValid = true;
    let errorMessage = '';

    // Required validation
    if (rules.required && (!value || value.trim() === '')) {
      isValid = false;
      errorMessage = errorMessages.required || `${name} is required`;
    }

    // Min length validation
    if (isValid && rules.minLength && value.length < rules.minLength) {
      isValid = false;
      errorMessage = errorMessages.minLength || `${name} must be at least ${rules.minLength} characters`;
    }

    // Max length validation
    if (isValid && rules.maxLength && value.length > rules.maxLength) {
      isValid = false;
      errorMessage = errorMessages.maxLength || `${name} must be less than ${rules.maxLength} characters`;
    }

    // Pattern validation
    if (isValid && rules.pattern && !rules.pattern.test(value)) {
      isValid = false;
      errorMessage = errorMessages.pattern || `${name} format is invalid`;
    }

    // Email validation
    if (isValid && rules.isEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      isValid = false;
      errorMessage = errorMessages.isEmail || `${name} must be a valid email address`;
    }

    // Phone validation
    if (isValid && rules.isPhone && !/^\+?[0-9]{10,15}$/.test(value.replace(/\s/g, ''))) {
      isValid = false;
      errorMessage = errorMessages.isPhone || `${name} must be a valid phone number`;
    }

    // Custom validation
    if (isValid && rules.custom && !rules.custom(value)) {
      isValid = false;
      errorMessage = errorMessages.custom || `${name} is invalid`;
    }

    // Update errors state
    setErrors(prev => ({
      ...prev,
      [name]: isValid ? '' : errorMessage
    }));

    return isValid;
  };

  /**
   * Validates multiple fields
   * @param fieldsToValidate Object with field names as keys and validation rules as values
   * @param values Object with field values
   * @param errorMessages Custom error messages
   * @returns True if all fields are valid, false otherwise
   */
  const validateForm = (
    fieldsToValidate: { [key: string]: ValidationRules },
    values: { [key: string]: string },
    errorMessages: { [key: string]: { [rule: string]: string } } = {}
  ): boolean => {
    let isFormValid = true;
    const newErrors: ValidationErrors = {};

    // Validate each field
    Object.entries(fieldsToValidate).forEach(([fieldName, rules]) => {
      const isFieldValid = validateField(
        fieldName,
        values[fieldName] || '',
        rules,
        errorMessages[fieldName] || {}
      );

      if (!isFieldValid) {
        isFormValid = false;
        newErrors[fieldName] = errors[fieldName];
      }
    });

    setErrors(newErrors);
    return isFormValid;
  };

  /**
   * Clears all errors
   */
  const clearErrors = () => {
    setErrors({});
  };

  /**
   * Sets a specific error
   * @param name Field name
   * @param message Error message
   */
  const setError = (name: string, message: string) => {
    setErrors(prev => ({
      ...prev,
      [name]: message
    }));
  };

  return {
    errors,
    validateField,
    validateForm,
    clearErrors,
    setError
  };
}
