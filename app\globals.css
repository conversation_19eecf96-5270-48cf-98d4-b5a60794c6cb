@import "tailwindcss";

/* CSS variables for better performance */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #52bcc3;

  /* Responsive spacing variables */
  --container-padding-mobile: 1rem;
  --container-padding-tablet: 1.5rem;
  --container-padding-desktop: 2rem;

  /* Responsive font size variables */
  --font-size-sm-mobile: 0.875rem;
  --font-size-base-mobile: 1rem;
  --font-size-lg-mobile: 1.125rem;
  --font-size-xl-mobile: 1.25rem;
  --font-size-2xl-mobile: 1.5rem;

  --font-size-sm-desktop: 0.875rem;
  --font-size-base-desktop: 1rem;
  --font-size-lg-desktop: 1.125rem;
  --font-size-xl-desktop: 1.375rem;
  --font-size-2xl-desktop: 1.75rem;
}

/* Theme variables */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --font-sans: var(--font-geist-sans);
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Body styles with responsive font sizing */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--font-size-base-mobile);
}

@media (min-width: 768px) {
  body {
    font-size: var(--font-size-base-desktop);
  }
}

/* Loading bar animation */
@keyframes loading-bar {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

.animate-loading-bar {
  animation: loading-bar 1s ease-in-out;
}

/* Page transition effects */
.page-transition-enter {
  opacity: 0;
}
.page-transition-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}
.page-transition-exit {
  opacity: 1;
}
.page-transition-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* Responsive container for consistent layout */
.fixed-container {
  position: relative;
  min-height: 430px; /* Reduced from 450px */
  max-height: 92vh;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: var(--container-padding-mobile);
  padding-top: calc(var(--container-padding-mobile) + 0.25rem); /* Reduced from 0.5rem */
  padding-bottom: calc(var(--container-padding-mobile) + 0.25rem); /* Reduced from 0.5rem */
  margin-top: 0.25rem; /* Added small margin on mobile */
}

/* Ensure top navigation stays in place */
.fixed-container > div {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

/* Responsive top bar with consistent position and size */
.fixed-top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 30; /* Increased z-index to ensure it's above other elements */
  height: 65px; /* Slightly smaller on very small screens */
  min-height: 60px; /* Ensure minimum height */
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  overflow: visible; /* Changed to visible to allow tooltips to overflow */
}

/* Ensure the inner content of the top bar doesn't overflow */
.fixed-top-bar > div {
  width: 100%;
  height: 100%;
  overflow: visible; /* Allow tooltips to be visible */
}

/* Make sure all containers allow overflow for tooltips */
.fixed-container,
.fixed-top-bar,
.fixed-top-bar > div,
.progress-bar-container {
  overflow: visible !important; /* Override any other overflow settings */
}

/* Add a class for the progress bar container to ensure tooltips are visible */
.progress-bar-container {
  position: relative;
  z-index: 40; /* Higher than the top bar */
}

/* Ensure milestone circles can show tooltips */
.progress-bar-container .absolute {
  position: absolute;
  overflow: visible;
}

@media (min-width: 640px) {
  .fixed-top-bar {
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
  }
}

@media (min-width: 768px) {
  .fixed-top-bar {
    border-top-left-radius: 40px;
    border-top-right-radius: 40px;
  }
}

/* Content area with consistent top margin */
.content-area {
  margin-top: 65px; /* Match the height of the fixed-top-bar */
  padding-top: 0.5rem;
  overflow-y: auto;
  flex-grow: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-bottom: 0.5rem;
  min-height: 350px; /* Ensure minimum height for content */
}

/* Responsive media queries */
@media (min-width: 640px) {
  .fixed-container {
    min-height: 480px; /* Reduced from 500px */
    padding: var(--container-padding-tablet);
    padding-top: calc(var(--container-padding-tablet) + 0.5rem); /* Reduced from 0.75rem */
    padding-bottom: calc(var(--container-padding-tablet) + 0.5rem); /* Reduced from 0.75rem */
    margin-top: 0.75rem; /* Reduced from 1rem */
    max-height: 90vh; /* Reduced from 92vh */
  }

  .fixed-top-bar {
    height: 75px; /* Slightly taller on larger screens */
  }

  .content-area {
    margin-top: 75px; /* Match the height of the fixed-top-bar */
    padding-top: 0.75rem;
  }
}

@media (min-width: 768px) {
  .fixed-container {
    min-height: 520px; /* Reduced from 550px */
    padding: var(--container-padding-desktop);
    padding-top: calc(var(--container-padding-desktop) + 0.5rem); /* Reduced from 1rem */
    padding-bottom: calc(var(--container-padding-desktop) + 0.5rem); /* Reduced from 1rem */
    margin-top: 1rem; /* Reduced from 2rem to move container upward */
    max-height: 88vh; /* Reduced from 92vh to prevent scrolling */
  }

  .fixed-top-bar {
    height: 80px; /* Taller on medium screens */
  }

  .content-area {
    margin-top: 80px; /* Match the height of the fixed-top-bar */
    padding-top: 0.75rem;
  }
}

/* Add a new media query for large desktop screens */
@media (min-width: 1024px) {
  .fixed-container {
    min-height: 560px; /* Reduced from 580px */
    margin-top: 1rem; /* Reduced from 1.5rem to move container upward */
    max-height: 80vh; /* Reduced from 85vh to prevent scrolling */
  }

  .fixed-top-bar {
    height: 85px; /* Tallest on large screens */
  }

  .content-area {
    margin-top: 85px; /* Match the height of the fixed-top-bar */
    padding-top: 0.75rem;
    min-height: 400px; /* Increased from 350px for larger screens */
  }
}

/* Add a new media query for extra large desktop screens */
@media (min-width: 1280px) {
  .fixed-container {
    min-height: 580px;
    margin-top: 0.5rem; /* Further reduced to move container upward */
    display: flex;
    align-items: center; /* Center container vertically */
    justify-content: center; /* Center container horizontally */
  }

  .content-area {
    min-height: 420px; /* Increased for extra large screens */
    padding-top: 0;
  }
}

/* Landscape mode adjustments for mobile devices */
@media (max-height: 500px) and (orientation: landscape) {
  .fixed-top-bar {
    height: 50px; /* Smaller height in landscape mode */
    min-height: 50px;
  }

  .content-area {
    margin-top: 50px; /* Match the height of the fixed-top-bar */
    min-height: 280px; /* Smaller minimum height in landscape */
  }

  .fixed-container {
    min-height: 350px; /* Smaller container in landscape */
    max-height: 95vh;
  }
}

/* Fix autofill styling to ensure text is visible */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 1000px white inset !important; /* white bg */
  -webkit-text-fill-color: #000 !important;                /* black text */
  caret-color: #000 !important;                            /* cursor color */
  transition: background-color 9999s ease-in-out 0s !important;
}

/* Dark mode autofill styling */
@media (prefers-color-scheme: dark) {
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 1000px #ffffff inset !important; /* dark bg */
    -webkit-text-fill-color: #323232 !important;               /* white text */
    caret-color: #fff !important;                           /* cursor color */
  }

  input, textarea, select {
    color: #e0e0e0 !important;
    border-color: #666 !important;
  }

  ::placeholder {
    color: #aaa !important;
  }
}

/* Improve input field visibility */
input, textarea, select {
  color: #333 !important;
  font-weight: 500;
}

/* Ensure placeholder text has good contrast */
::placeholder {
  color: #666 !important;
  opacity: 1 !important;
}
