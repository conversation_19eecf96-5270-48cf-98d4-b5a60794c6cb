import { NextRequest, NextResponse } from 'next/server';
import acceptLanguage from 'accept-language';
import { fallbackLng, languages } from './app/i18n/settings';

// Configure accept-language once
acceptLanguage.languages(languages);

// Optimize matcher pattern to exclude more static files
export const config = {
  matcher: [
    // Only run on html pages, not on api routes or static files
    '/((?!api|_next|images|favicon.ico|.*\\.).*)'
  ]
};

const cookieName = 'i18next';

// Global cache with a reasonable size limit
const CACHE_SIZE_LIMIT = 100;
const languageCache = new Map<string, string>();

export function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Fast path: extract language from URL if present
  const pathLangMatch = pathname.match(/^\/([a-z]{2})(?:\/|$)/);
  const pathLang = pathLangMatch ? pathLangMatch[1] : null;

  // If URL has valid language prefix, just set cookie if needed and continue
  if (pathLang && languages.includes(pathLang)) {
    const response = NextResponse.next();
    const currentCookie = req.cookies.get(cookieName)?.value;

    // Only set cookie if different from current to avoid unnecessary writes
    if (currentCookie !== pathLang) {
      response.cookies.set(cookieName, pathLang, {
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: '/'
      });
    }
    return response;
  }

  // Determine preferred language
  let lng = fallbackLng;

  // Check cookie first (fastest)
  const cookieValue = req.cookies.get(cookieName)?.value;
  if (cookieValue && languages.includes(cookieValue)) {
    lng = cookieValue;
  } else {
    // Check Accept-Language header
    const acceptLangHeader = req.headers.get('Accept-Language') || '';

    // Use cache to avoid repeated parsing
    if (languageCache.has(acceptLangHeader)) {
      lng = languageCache.get(acceptLangHeader)!;
    } else {
      // Parse Accept-Language header
      lng = acceptLanguage.get(acceptLangHeader) || fallbackLng;

      // Manage cache size to prevent memory leaks
      if (languageCache.size >= CACHE_SIZE_LIMIT) {
        // Remove oldest entry (first key)
        const firstKey = languageCache.keys().next().value;
        languageCache.delete(firstKey);
      }

      languageCache.set(acceptLangHeader, lng);
    }
  }

  // Redirect to URL with language prefix
  return NextResponse.redirect(
    new URL(
      `/${lng}${pathname === '/' ? '' : pathname}${req.nextUrl.search}`,
      req.url
    )
  );
}
