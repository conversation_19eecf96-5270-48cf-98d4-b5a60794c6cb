"use client";

import { useState, useEffect, useCallback } from "react";
import { getTranslation } from "../i18n/translations";
import { signupService } from "../services/signupService";
import { updateUserDetails, setUserStage } from "../services/api";
import { useAuth } from "../context/ApiAuthContext";
import ConversationPage from "./OnboardingAssistantScreen";
import DynamicAvatar from "./AssistantAvatar";
import PageLayout from "./PageLayout";
import { UserStage } from "./UserJourneyPage";

// Client component that receives the lng parameter directly
export default function AvatarSelectionPage({ lng }: { lng: string }) {
  const { user } = useAuth();
  const [error, setError] = useState("");
  const [currentAvatar, setCurrentAvatar] = useState<"male" | "female">("female");
  const [showConversation, setShowConversation] = useState(false);
  const [firstName, setFirstName] = useState<string>("User");
  const [isLoading, setIsLoading] = useState(true);

  // Use the t function from our static translations
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Get user's first name and determine initial screen
  useEffect(() => {
    // Start with loading state
    setIsLoading(true);

    // Simple first name extraction
    if (user && 'first_name' in user) {
      setFirstName(user.first_name as string);
    } else if (user?.details && 'first_name' in user.details) {
      setFirstName(user.details.first_name as string);
    } else {
      const userDetails = signupService.getUserDetails();
      if (userDetails?.first_name) {
        setFirstName(userDetails.first_name);
      }
    }

    // Check if user already has an avatar selected
    if (user?.details?.avatar_gender) {
      // For existing users who have already selected an avatar, use their selection
      setCurrentAvatar(user.details.avatar_gender as "male" | "female");

      // If user is at stage 2 or higher, show conversation screen directly
      if (user.current_stage >= 2) {
        setShowConversation(true);
      }
    } else {
      // For new users, ensure female avatar is shown initially
      setCurrentAvatar("female");
    }

    // Done loading
    setIsLoading(false);
  }, [user]);

  // Handle avatar navigation with memoized callbacks
  const handleAvatarChange = useCallback((newAvatar: "male" | "female") => {
    setCurrentAvatar(newAvatar);
  }, []);

  const handleNextAvatar = useCallback(() => {
    handleAvatarChange(currentAvatar === "female" ? "male" : "female");
  }, [currentAvatar, handleAvatarChange]);

  const handlePrevAvatar = useCallback(() => {
    handleAvatarChange(currentAvatar === "female" ? "male" : "female");
  }, [currentAvatar, handleAvatarChange]);

  // Handle continue button click with memoized callback
  const handleContinue = useCallback(async () => {
    // Store avatar gender in the signup service
    signupService.setAvatarGender(currentAvatar);

    try {
      // Update the avatar gender
      await updateUserDetails({
        avatar_gender: currentAvatar
      });

      // Update the user's stage to 2
      await setUserStage(2);

      // Show the conversation page
      setShowConversation(true);
    } catch (error) {
      console.error('Failed to update avatar selection or stage:', error);
      setError("Failed to update avatar selection. Please try again.");
    }
  }, [currentAvatar]);

  // Language switching is now handled by PageLayout

  // Show loading indicator while determining which screen to show
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#52bcc3]"></div>
      </div>
    );
  }

  // If we should show the conversation page, render it instead
  if (showConversation) {
    return (
      <ConversationPage
        lng={lng}
        avatarGender={currentAvatar}
      />
    );
  }

  // No need to calculate progress width anymore as it's handled by the ProgressBar component

  return (
    <PageLayout
      currentStage={UserStage.AVATAR_SELECTION}
      containerStyle="rounded"
      showActionButtons={true}
    >
      {/* Language selector is now handled by PageLayout */}

      <div className="text-center max-w-md mx-auto">
            {/* Welcome text */}
            <h2 className="text-lg font-semibold text-gray-800 mb-4 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] bg-clip-text text-transparent inline-block">
              {t("hi_user").replace("{name}", firstName)}<br />
              {t("avatar_selection_title").replace("{name}", "") || "Who would you like to become your personal assistant?"}
            </h2>

            {error && (
              <div className="p-3 mb-4 mx-4 bg-red-500/10 border border-red-500/30 text-red-500 rounded-md text-sm">
                {error}
              </div>
            )}

            <div className="flex flex-col justify-center items-center">
              {/* Avatar selection UI */}
              <div className="flex justify-center items-center mb-4 relative w-full">
                {/* Left arrow */}
                <button
                  onClick={handlePrevAvatar}
                  className={`absolute left-1/4 transform -translate-x-1/2 p-2 ${currentAvatar === 'male' ? 'text-gray-600 hover:text-[#52bcc3]' : 'text-gray-300'} transition-colors bg-white rounded-full shadow-md z-10`}
                  aria-label="Previous avatar"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-7 h-7">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                  </svg>
                </button>

                <div className="relative mx-auto w-44 h-44">
                  <DynamicAvatar
                    gender={currentAvatar}
                    isActive={true}
                    audioLevel={0.3}
                  />
                </div>

                {/* Right arrow */}
                <button
                  onClick={handleNextAvatar}
                  className={`absolute right-1/4 transform translate-x-1/2 p-2 ${currentAvatar === 'female' ? 'text-gray-600 hover:text-[#52bcc3]' : 'text-gray-300'} transition-colors bg-white rounded-full shadow-md z-10`}
                  aria-label="Next avatar"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-7 h-7">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                  </svg>
                </button>
              </div>

              {/* Avatar selection dots */}
              <div className="flex justify-center space-x-6 mb-4">
                <button
                  onClick={() => handleAvatarChange('female')}
                  className={`w-3 h-3 rounded-full ${currentAvatar === 'female' ? 'bg-[#52bcc3] ring-2 ring-[#52bcc3]/30' : 'bg-gray-300'} transition-all duration-300 transform ${currentAvatar === 'female' ? 'scale-110' : ''} focus:outline-none`}
                  aria-label="Select female avatar"
                ></button>
                <button
                  onClick={() => handleAvatarChange('male')}
                  className={`w-3 h-3 rounded-full ${currentAvatar === 'male' ? 'bg-[#52bcc3] ring-2 ring-[#52bcc3]/30' : 'bg-gray-300'} transition-all duration-300 transform ${currentAvatar === 'male' ? 'scale-110' : ''} focus:outline-none`}
                  aria-label="Select male avatar"
                ></button>
              </div>

              {/* Avatar type label */}
              <div className="mb-4 text-base font-medium text-gray-800 bg-[#52bcc3]/10 py-1.5 px-4 rounded-full inline-block">
                {currentAvatar === "female" ? t("female_avatar") || "Female Avatar" : t("male_avatar") || "Male Avatar"}
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={handleContinue}
                className="min-w-[120px] py-2.5 px-6 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer text-white text-base rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap"
              >
                {t("continue_button") || "Continue"}
              </button>
            </div>
          </div>
    </PageLayout>
  );
}