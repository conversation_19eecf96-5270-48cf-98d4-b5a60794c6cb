"use client";

import { useState, useCallback, useEffect } from "react";
import { getTranslation } from "../i18n/translations";
import { signupService } from "../services/signupService";
import { updateUserDetails, setUserStage } from "../services/api";
import { useAuth } from "../context/ApiAuthContext";
import DynamicAvatar from "./AssistantAvatar";

// Component for avatar selection functionality
export default function AvatarSelection({
  lng,
  initialAvatar = "female",
  onAvatarSelected
}: {
  lng: string;
  initialAvatar?: "male" | "female";
  onAvatarSelected: (avatarGender: "male" | "female") => void;
}) {
  const { user } = useAuth();
  const [error, setError] = useState("");
  // Always default to female avatar for new users, but respect initialAvatar if provided
  const [currentAvatar, setCurrentAvatar] = useState<"male" | "female">(initialAvatar || "female");
  const [firstName, setFirstName] = useState<string>("User");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use the t function from our static translations
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Get user's first name
  useEffect(() => {
    if (user && 'first_name' in user) {
      setFirstName(user.first_name as string);
    } else if (user?.details && 'first_name' in user.details) {
      setFirstName(user.details.first_name as string);
    } else {
      const userDetails = signupService.getUserDetails();
      if (userDetails?.first_name) {
        setFirstName(userDetails.first_name);
      }
    }
  }, [user]);

  // Update currentAvatar when initialAvatar prop changes
  useEffect(() => {
    // If initialAvatar is provided, update currentAvatar
    if (initialAvatar) {
      setCurrentAvatar(initialAvatar);
    } else {
      // Default to female avatar if initialAvatar is not provided
      setCurrentAvatar("female");
    }
  }, [initialAvatar]);

  // Handle avatar navigation with memoized callbacks
  const handleAvatarChange = useCallback((newAvatar: "male" | "female") => {
    setCurrentAvatar(newAvatar);
  }, []);

  const handleNextAvatar = useCallback(() => {
    handleAvatarChange(currentAvatar === "female" ? "male" : "female");
  }, [currentAvatar, handleAvatarChange]);

  const handlePrevAvatar = useCallback(() => {
    handleAvatarChange(currentAvatar === "female" ? "male" : "female");
  }, [currentAvatar, handleAvatarChange]);

  // Handle continue button click with memoized callback
  const handleContinue = useCallback(async () => {
    setIsSubmitting(true);

    // Store avatar gender in the signup service
    signupService.setAvatarGender(currentAvatar);

    try {
      // Update the avatar gender
      await updateUserDetails({
        avatar_gender: currentAvatar
      });

      // Update the user's stage to 2
      await setUserStage(2);

      // Notify parent component that avatar selection is complete
      onAvatarSelected(currentAvatar);
    } catch (error) {
      console.error('Failed to update avatar selection or stage:', error);
      setError(t("error_avatar_update") || "Failed to update avatar selection. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [currentAvatar, onAvatarSelected]);

  // No need to calculate progress width anymore as it's handled by the ProgressBar component

  return (
    <div className="text-center max-w-xs sm:max-w-sm md:max-w-md mx-auto px-2 sm:px-0">
      {/* Welcome text */}
      <h2 className="text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] bg-clip-text text-transparent inline-block">
        {t("hi_user").replace("{name}", firstName)}<br />
        {t("avatar_selection_title").replace("{name}", "") || "Who would you like to become your personal assistant?"}
      </h2>

      {error && (
        <div className="p-2 sm:p-3 mb-3 sm:mb-4 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 text-red-500 rounded-md text-xs sm:text-sm">
          {error}
        </div>
      )}

      <div className="flex flex-col justify-center items-center">
        {/* Avatar selection UI */}
        <div className="flex justify-center items-center mb-3 sm:mb-4 relative w-full">
          {/* Left arrow */}
          <button
            onClick={handlePrevAvatar}
            className={`absolute left-1/4 transform -translate-x-1/2 p-1 sm:p-2 ${currentAvatar === 'male' ? 'text-gray-600 hover:text-[#52bcc3]' : 'text-gray-300'} transition-colors bg-white rounded-full shadow-md z-10`}
            aria-label="Previous avatar"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
            </svg>
          </button>

          <div className="relative mx-auto w-32 h-32 sm:w-40 sm:h-40 md:w-44 md:h-44">
            <DynamicAvatar
              gender={currentAvatar}
              isActive={true}
              audioLevel={0.3}
            />
          </div>

          {/* Right arrow */}
          <button
            onClick={handleNextAvatar}
            className={`absolute right-1/4 transform translate-x-1/2 p-1 sm:p-2 ${currentAvatar === 'female' ? 'text-gray-600 hover:text-[#52bcc3]' : 'text-gray-300'} transition-colors bg-white rounded-full shadow-md z-10`}
            aria-label="Next avatar"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7">
              <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
          </button>
        </div>

        {/* Avatar selection dots */}
        <div className="flex justify-center space-x-4 sm:space-x-6 mb-3 sm:mb-4">
          <button
            onClick={() => handleAvatarChange('female')}
            className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full ${currentAvatar === 'female' ? 'bg-[#52bcc3] ring-1 sm:ring-2 ring-[#52bcc3]/30' : 'bg-gray-300'} transition-all duration-300 transform ${currentAvatar === 'female' ? 'scale-110' : ''} focus:outline-none`}
            aria-label="Select female avatar"
          ></button>
          <button
            onClick={() => handleAvatarChange('male')}
            className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full ${currentAvatar === 'male' ? 'bg-[#52bcc3] ring-1 sm:ring-2 ring-[#52bcc3]/30' : 'bg-gray-300'} transition-all duration-300 transform ${currentAvatar === 'male' ? 'scale-110' : ''} focus:outline-none`}
            aria-label="Select male avatar"
          ></button>
        </div>

        {/* Avatar type label */}
        <div className="mb-3 sm:mb-4 text-sm sm:text-base font-medium text-gray-800 bg-[#52bcc3]/10 py-1 sm:py-1.5 px-3 sm:px-4 rounded-full inline-block">
          {currentAvatar === "female" ? t("female_avatar") || "Female Avatar" : t("male_avatar") || "Male Avatar"}
        </div>
      </div>

      <div className="mt-4 sm:mt-6">
        <button
          onClick={handleContinue}
          disabled={isSubmitting}
          className={`min-w-[100px] sm:min-w-[120px] py-2 sm:py-2.5 px-4 sm:px-6 ${
            isSubmitting
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer"
          } text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-3 w-3 sm:h-4 sm:w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t("processing") || "Processing..."}
            </span>
          ) : (
            t("continue_button") || "Continue"
          )}
        </button>
      </div>
    </div>
  );
}
