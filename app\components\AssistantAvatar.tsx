"use client";

import React, { useState, useEffect } from 'react';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';

interface DynamicAvatarProps {
  gender: 'male' | 'female';
  isActive?: boolean;
  isSpeaking?: boolean;
  audioLevel?: number;
  className?: string;
  onLottieRef?: (lottieInstance: any) => void;
  speed?: number;
  hoverEffect?: boolean;
  mode?: 'forward' | 'reverse' | 'bounce' | 'reverse-bounce';
  segment?: [number, number];
  useFrameInterpolation?: boolean;
  backgroundColor?: string;
  colorize?: boolean;
}

/**
 * DynamicAvatar component that displays a Lottie animation based on gender
 * and responds to conversation state and audio levels
 *
 * @param gender - 'male' or 'female' to determine which avatar to show
 * @param isActive - whether the avatar is currently active in conversation
 * @param isSpeaking - whether the assistant is currently speaking (controls freeze state)
 * @param audioLevel - current audio level (0-1) to scale the avatar
 * @param className - additional CSS classes
 * @param onLottieRef - callback to get the Lottie instance reference
 */
const DynamicAvatar: React.FC<DynamicAvatarProps> = ({
  gender,
  isActive = false,
  isSpeaking = false,
  audioLevel = 0,
  className = '',
  onLottieRef,
  speed = 1,
  hoverEffect = false,
  mode = 'forward',
  segment,
  useFrameInterpolation = true,
  backgroundColor,
  colorize = false,
}) => {
  // State for the Lottie instance
  const [lottieInstance, setLottieInstance] = useState<any>(null);

  // State for dynamic scaling based on audio level
  const [scale, setScale] = useState(1);

  // State for hover effect
  const [isHovered, setIsHovered] = useState(false);

  // Calculate the avatar path based on gender
  const avatarPath = `/images/avatars/${gender}_avatar.json`;

  // Callback for getting the Lottie instance
  const dotLottieRefCallback = (dotLottie: any) => {
    if (dotLottie) {
      setLottieInstance(dotLottie);
      if (onLottieRef) {
        onLottieRef(dotLottie);
      }
    }
  };

  // Effect to handle audio level changes
  useEffect(() => {
    if (isActive && audioLevel > 0) {
      // Scale between 1.0 and 1.3 based on audio level
      const newScale = 1 + (audioLevel * 0.3);
      setScale(newScale);
    } else {
      // Reset to default size when inactive or no audio
      setScale(1);
    }
  }, [isActive, audioLevel]);

  // Effect to handle play/pause based on active state, speaking state, and hover state
  useEffect(() => {
    if (!lottieInstance) return;

    console.log('DynamicAvatar: Animation effect running, isActive =', isActive, 'isSpeaking =', isSpeaking, 'isHovered =', isHovered);

    if (isHovered) {
      // Hover state takes precedence
      console.log('DynamicAvatar: Hover state active, playing at speed', speed * 1.5);
      lottieInstance.play();
      lottieInstance.setSpeed(speed * 1.5);
    } else if (isActive && isSpeaking) {
      // Active and speaking - play at normal speed
      console.log('DynamicAvatar: Active and speaking, playing at normal speed', speed);
      lottieInstance.play();
      lottieInstance.setSpeed(speed);
    } else if (isActive && !isSpeaking) {
      // Active but not speaking - freeze the animation
      console.log('DynamicAvatar: Active but not speaking, freezing animation');
      lottieInstance.pause();
    } else {
      // Inactive - play at slow speed
      console.log('DynamicAvatar: Inactive state, playing at slow speed 0.2');
      lottieInstance.play();
      lottieInstance.setSpeed(0.2);
    }

    return () => {
      // Reset speed when component unmounts or dependencies change
      if (lottieInstance) {
        lottieInstance.setSpeed(speed);
      }
    };
  }, [lottieInstance, isActive, isSpeaking, isHovered, speed]);

  // Log when isActive or isSpeaking changes
  useEffect(() => {
    console.log('DynamicAvatar: isActive changed to', isActive, 'isSpeaking changed to', isSpeaking);
  }, [isActive, isSpeaking]);

  // Base styles for the avatar container
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    transition: 'transform 0.2s ease-out, opacity 0.3s ease-in-out',
    transform: `scale(${scale})`,
    opacity: isActive ? 1 : 0.4, // 40% opacity when inactive
    // High quality rendering (always enabled)
    backfaceVisibility: 'hidden',
    WebkitFontSmoothing: 'antialiased',
    WebkitBackfaceVisibility: 'hidden',
    perspective: 1000,
  };

  // Styles for the avatar wrapper
  const avatarWrapperStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    transition: 'all 0.3s ease-in-out',
    transform: 'translateZ(0)', // Force GPU acceleration for higher quality
    willChange: 'transform, opacity', // Optimize for animations
  };

  // Handle mouse events for hover effect
  const handleMouseEnter = () => {
    if (hoverEffect && lottieInstance) {
      setIsHovered(true);
      lottieInstance.play();
      lottieInstance.setSpeed(speed * 1.5); // Speed up on hover
    }
  };

  const handleMouseLeave = () => {
    if (hoverEffect && lottieInstance) {
      setIsHovered(false);
      if (!isActive) {
        lottieInstance.play();
        lottieInstance.setSpeed(0.2); // Return to slow speed when inactive
      } else if (isSpeaking) {
        lottieInstance.play();
        lottieInstance.setSpeed(speed); // Return to normal speed when active and speaking
      } else {
        lottieInstance.pause(); // Freeze when active but not speaking
      }
    }
  };

  return (
    <div
      className={`dynamic-avatar ${className}`}
      style={containerStyle}
      aria-label={`${gender} avatar ${isActive ? (isSpeaking ? 'active and speaking' : 'active but not speaking') : 'inactive'}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Avatar content */}
      <div style={avatarWrapperStyle}>
        <div style={{
          width: '100%',
          height: '100%',
          filter: colorize ? 'saturate(2) brightness(1.3) contrast(1.2) hue-rotate(10deg)' : 'none',
          transition: 'filter 0.3s ease-in-out',
        }}>
          <DotLottieReact
            src={avatarPath}
            loop
            autoplay
            style={{
              width: '100%',
              height: '100%',
            }}
            dotLottieRefCallback={dotLottieRefCallback}
            speed={speed}
            mode={mode}
            segment={segment}
            useFrameInterpolation={useFrameInterpolation}
            backgroundColor={backgroundColor}
          />
        </div>
      </div>
    </div>
  );
};

export default DynamicAvatar;
