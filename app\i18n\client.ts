'use client';

import { useEffect, useMemo } from 'react';
import i18next from 'i18next';
import { initReactI18next, useTranslation as useTranslationOrg } from 'react-i18next';
import resourcesToBackend from 'i18next-resources-to-backend';
import { getOptions } from './settings';
import enCommon from '../../public/locales/en/common.json';
import deCommon from '../../public/locales/de/common.json';

// Pre-load translations to ensure client-side consistency
const resources = {
  en: { common: enCommon },
  de: { common: deCommon }
};

// Initialize i18next for client-side with optimized settings
const i18nInstance = i18next
  .use(initReactI18next)
  .use(resourcesToBackend((language: string, namespace: string) =>
    import(`../../public/locales/${language}/${namespace}.json`)
  ));

// Initialize only once with optimized settings
let initialized = false;
if (!initialized) {
  i18nInstance.init({
    ...getOptions(),
    resources, // Include pre-loaded resources to avoid network requests
    load: 'currentOnly',
    debug: false,
    react: {
      useSuspense: false,
      bindI18n: 'languageChanged', // Only trigger on language change
      bindI18nStore: '', // Disable store binding for better performance
      transEmptyNodeValue: '', // Empty string for empty nodes
    },
    // Optimize loading behavior
    partialBundledLanguages: true,
    saveMissing: false,
  });
  initialized = true;
}

// Optimized translation hook with memoization
export function useTranslation(ns: string, lng?: string) {
  const ret = useTranslationOrg(ns);
  const { i18n } = ret;

  // Change language only when needed
  useEffect(() => {
    if (lng && i18n.language !== lng) {
      i18n.changeLanguage(lng);
    }
  }, [lng, i18n]);

  // Memoize the return value to prevent unnecessary re-renders
  return useMemo(() => ret, [ret.i18n.language, ns]);
}
