"use client";

import { useState } from "react";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import { getTranslation } from "../i18n/translations";
import { requestPasswordReset } from "../services/api";

export default function ForgotPasswordPage({ lng }: { lng: string }) {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  // Helper function to get translation
  const t = (key: string) => getTranslation(key, lng);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setError(t("email_required") || "Email is required");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Call the API to request a password reset
      await requestPasswordReset(email);
      setSuccess(true);
    } catch (err) {
      setError(t("error_generic") || "An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-gradient-to-b from-white to-gray-50">
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md p-6 sm:p-8 md:p-10 space-y-6 sm:space-y-8 bg-white border border-gray-200 rounded-2xl sm:rounded-3xl shadow-lg">
        <div className="text-center">
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-10 sm:h-12 md:h-14 w-auto rounded-xl overflow-hidden"
            />
          </div>

          <h2 className="text-xl sm:text-2xl font-semibold text-gray-800 mb-4 sm:mb-6">
            {lng === "de" ? "Passwort zurücksetzen" : "Reset Password"}
          </h2>

          {!success ? (
            <>
              <p className="mb-6 sm:mb-8 text-gray-700 px-2 sm:px-4 text-sm sm:text-base">
                {lng === "de"
                  ? "Geben Sie Ihre E-Mail-Adresse ein, und wir senden Ihnen einen Link zum Zurücksetzen Ihres Passworts."
                  : "Enter your email address and we'll send you a link to reset your password."}
              </p>

              {error && (
                <div className="p-3 sm:p-4 mb-4 sm:mb-5 bg-red-50 border border-red-200 rounded-lg text-red-600 text-xs sm:text-sm shadow-sm">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {error}
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-5 sm:space-y-6">
                <div className="relative">
                  <label className="block text-left text-sm font-medium text-gray-700 mb-1">
                    {lng === "de" ? "E-Mail-Adresse" : "Email Address"}
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={lng === "de" ? "Ihre E-Mail-Adresse eingeben" : "Enter your email address"}
                    className="w-full px-4 py-3 border border-gray-300 rounded-full text-sm sm:text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#52bcc3] focus:border-transparent transition-all"
                    required
                  />
                </div>

                <div className="pt-2 sm:pt-3">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full py-3 sm:py-3.5 px-4 sm:px-6 text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 ${
                      isSubmitting
                        ? 'bg-gray-400 cursor-not-allowed opacity-70'
                        : 'bg-[#52bcc3] hover:bg-[#3da8af] hover:shadow-md'
                    }`}
                  >
                    {isSubmitting
                      ? (lng === "de" ? "Wird gesendet..." : "Submitting...")
                      : (lng === "de" ? "Link senden" : "Send Reset Link")}
                  </button>
                </div>
              </form>
            </>
          ) : (
            <div className="bg-green-50 border border-green-200 rounded-lg p-5 text-green-700 shadow-sm">
              <div className="flex items-center justify-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="mb-4 font-medium text-base">
                {lng === "de"
                  ? "Wenn ein Konto mit dieser E-Mail-Adresse existiert, haben wir einen Link zum Zurücksetzen des Passworts gesendet."
                  : "If an account with this email exists, we've sent a password reset link."}
              </p>
              <p className="text-sm">
                {lng === "de"
                  ? "Bitte überprüfen Sie Ihren Posteingang und folgen Sie den Anweisungen."
                  : "Please check your inbox and follow the instructions."}
              </p>
            </div>
          )}

{/*           <div className="text-center text-xs sm:text-sm text-gray-700 mt-5 sm:mt-6">
            <Link href={`/${lng}/login`} className="text-[#52bcc3] hover:underline font-medium">
              {lng === "de" ? "Zurück zur Anmeldung" : "Back to Login"}
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  );
}