import FinalThankYouScreen from "../../components/CompletionScreen";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Thank <PERSON> <PERSON> <PERSON><PERSON><PERSON>",
    description: "Thank you for using Lumal<PERSON>"
  };
}

// Server component that passes the lng parameter to the client component
export default async function FinalThankYou({
  params,
}: {
  params: { lng: string };
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <FinalThankYouScreen lng={lng} />;
}
