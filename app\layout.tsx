import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>ei<PERSON> } from "next/font/google";
import "./globals.css";

// Optimize font loading by using only one font with minimal configuration
// Removed Geist_Mono completely as it's not needed for most content
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "optional", // Use 'optional' for better performance than 'swap'
  preload: false, // Only preload when needed
  weight: ["400"], // Only load the regular weight
  fallback: ["Arial", "sans-serif"], // Provide system fallbacks
  adjustFontFallback: true, // Adjust metrics to reduce layout shift
});

// Optimize metadata for better caching
export const metadata: Metadata = {
  title: "Luma Life",
  description: "Your personal AI everyday assistant",
  // Increase cache times for better performance
  other: {
    "Cache-Control": "public, max-age=86400, s-maxage=86400, stale-while-revalidate=604800",
  },
  // Add icons for browser tabs and mobile devices
  icons: {
    icon: '/images/logo_icon.png',
    shortcut: '/images/logo_icon.png',
    apple: '/images/logo_icon.png',
    other: {
      rel: 'apple-touch-icon-precomposed',
      url: '/images/logo_icon.png',
    },
  },
};

// Enhanced responsive viewport configuration
export const viewport = {
  width: "device-width",
  initialScale: 1,
  minimumScale: 1,
  viewportFit: "cover",
  // Allow users to zoom for accessibility
  maximumScale: 5,
  // Theme color matching the primary app color
  themeColor: "#52bcc3",
};

// Root layout component - no need for memo as it only renders once
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return children;
}
