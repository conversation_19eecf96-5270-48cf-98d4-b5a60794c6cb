# Lumalife Prototype Frontend

AI assistant for healthier aging of people with dementia.

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

### Environment Variables

Before running the application, you need to set up your environment variables:

1. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and set the following variables:
   - `NEXT_PUBLIC_API_URL`: The URL of your API server (e.g., `http://localhost:8000`)
   - `NEXT_PUBLIC_WS_URL`: The WebSocket URL (e.g., `ws://localhost:8000`)
   - Other variables as needed

**Important Notes:**
- The API URL must use the HTTP protocol (`http://` or `https://`)
- The WebSocket URL must use the WebSocket protocol (`ws://` or `wss://`)
- All URLs must be provided from environment variables, not hardcoded in the application

### Running the Development Server

After setting up your environment variables, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deployment

### Docker Deployment

This application can be deployed using Docker:

1. Build the Docker image:
   ```bash
   docker build -t kommittee-frontend .
   ```
   Note: The build process uses placeholder values for environment variables.

2. Run the container with environment variables:
   ```bash
   # Using an .env file
   docker run -p 3000:3000 --env-file .env kommittee-frontend

   # Or specifying environment variables directly
   docker run -p 3000:3000 \
     -e NEXT_PUBLIC_API_URL=http://your-api-server:8000 \
     -e NEXT_PUBLIC_WS_URL=ws://your-api-server:8000 \
     kommittee-frontend
   ```

### Environment Variables in Production

In production, make sure to set the environment variables correctly:

- Use the actual production URLs for `NEXT_PUBLIC_API_URL` and `NEXT_PUBLIC_WS_URL`
- For secure connections, use `https://` for API URLs and `wss://` for WebSocket URLs
- Never hardcode URLs in the application code; always use environment variables

## Troubleshooting

If you encounter issues with WebSocket connections:

1. Verify that your WebSocket URL uses the correct protocol (`ws://` or `wss://`)
2. Check that the API and WebSocket servers are running and accessible
3. Look for connection errors in the browser console
