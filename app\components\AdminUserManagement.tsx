"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getTranslation } from "../i18n/translations";
import { listUsers, bulkApproveUsers, approveUser, UserList, UserListParams, RoleEnum, UserStageEnum } from "../services/adminApi";

// Client component for the admin user management
export default function AdminUserManagement({ lng, embedded = false }: { lng: string, embedded?: boolean }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [users, setUsers] = useState<UserList[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserList | null>(null);

  // Loading states
  const [isApproving, setIsApproving] = useState(false);
  const [approvalError, setApprovalError] = useState("");
  const [approvalSuccess, setApprovalSuccess] = useState("");

  // Filter states
  const [filters, setFilters] = useState<UserListParams>({
    page: 1,
    limit: 100,  // Increased to maximum allowed by the API (100)
    sort_by: "created_at",
    sort_desc: true
  });

  // Use the t function from our static translations
  const t = (key: string) => getTranslation(key, lng);

  // Load users on component mount and when filters change
  useEffect(() => {
    // Use full page loading only on initial load
    const isInitialLoad = users.length === 0;
    fetchUsers(isInitialLoad);
  }, [filters]);

  // Track users data changes for production
  useEffect(() => {
    // User data tracking for production
  }, [users, filters, currentPage, totalPages, totalUsers, itemsPerPage]);

  // Function to fetch users with current filters
  const fetchUsers = async (showFullPageLoading = false) => {
    // Only show full page loading on initial load or explicit request
    if (showFullPageLoading) {
      setIsLoading(true);
    }

    // Always clear error
    setError("");

    // Use a local loading state for table-only loading
    const tableLoadingElement = document.getElementById('table-loading');
    if (tableLoadingElement && !showFullPageLoading) {
      tableLoadingElement.classList.remove('hidden');
    }

    try {
      const response = await listUsers(filters);

      // Ensure we have valid data
      if (response && response.items) {
        setUsers(response.items);
        setTotalUsers(response.total || 0);
        setCurrentPage(response.page || 1);
        setTotalPages(response.pages || 1);
        setItemsPerPage(response.limit || 10);
      } else if (Array.isArray(response)) {
        // Handle case where response is a direct array of users
        setUsers(response);

        // For direct array responses, we need to handle pagination differently
        // since the API doesn't return total count

        // Set current page from filters
        const currentPageValue = filters.page || 1;
        setCurrentPage(currentPageValue);

        // Set items per page from filters
        const itemsPerPageValue = filters.limit || 10;
        setItemsPerPage(itemsPerPageValue);

        // SIMPLIFIED PAGINATION APPROACH:
        // 1. We don't try to estimate total pages or total users
        // 2. We just focus on enabling/disabling Previous/Next buttons
        // 3. We consider there are more pages if we get a full page of results

        // Check if we got a full page of results
        const hasMorePages = response.length >= itemsPerPageValue;
        console.log(`Page ${currentPageValue}: Got ${response.length} items, limit is ${itemsPerPageValue}, hasMorePages: ${hasMorePages}`);

        // Set a flag to indicate if there are more pages
        // This will be used to enable/disable the Next button
        setTotalPages(hasMorePages ? currentPageValue + 1 : currentPageValue);

        // Set total users to the number of items we've seen so far
        // This is just for display purposes
        if (currentPageValue > 1) {
          // If we're on page > 1, calculate based on previous pages + current results
          const totalSoFar = (currentPageValue - 1) * itemsPerPageValue + response.length;
          setTotalUsers(totalSoFar);
          console.log(`Page ${currentPageValue}: Total users seen so far: ${totalSoFar}`);
        } else {
          // If we're on page 1, just use the number of items
          setTotalUsers(response.length);
          console.log(`Page 1: Total users seen so far: ${response.length}`);
        }
      } else {
        // Handle case where response is incomplete or invalid
        setUsers([]);
        setTotalUsers(0);
        setCurrentPage(1);
        setTotalPages(1);
        console.error("Received invalid response format:", response);
      }
    } catch (err: any) {
      console.error("Error fetching users:", err);
      setError(err.message || "Failed to load users");

      // Reset state on error
      setUsers([]);
      setTotalUsers(0);
      setCurrentPage(1);
      setTotalPages(1);
    } finally {
      setIsLoading(false);

      // Hide table loading indicator
      if (tableLoadingElement) {
        tableLoadingElement.classList.add('hidden');
      }
    }
  };

  // Handle filter changes
  const handleFilterChange = (name: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [name]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  // Pagination functions removed since we're showing all users

  // Handle sorting
  const handleSort = (column: string) => {
    // If clicking on the same column that's already being sorted
    if (filters.sort_by === column) {
      // Toggle the sort direction
      const newSortDesc = !filters.sort_desc;
      setFilters(prev => ({
        ...prev,
        sort_desc: newSortDesc
      }));
    } else {
      // If clicking on a new column, sort by that column in descending order by default
      setFilters(prev => ({
        ...prev,
        sort_by: column,
        sort_desc: true
      }));
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 100,  // Always use maximum limit allowed by API
      sort_by: "created_at",
      sort_desc: true
    });
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Invalid Date";
      }
      return date.toLocaleDateString();
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid Date";
    }
  };

  // Handle opening the user details modal
  const handleViewUser = (user: UserList) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  // Handle closing the user details modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  // Handle approving or rejecting a user
  const handleApproveUser = async (userId: number, approve: boolean) => {
    try {
      // Find the user in the list
      const user = users.find(u => u.id === userId);

      // Check if the user is an admin - cannot change approval status of admin users
      if (user && user.role === 'admin') {
        setApprovalError(t("cannot_modify_admin") || "Cannot modify approval status of admin users.");
        return;
      }

      setIsApproving(true);
      setApprovalError("");
      setApprovalSuccess("");

      // Call the individual user approve API
      await approveUser(userId, approve);

      // Show success message
      setApprovalSuccess(
        approve
          ? t("user_approved_success") || "User has been approved successfully."
          : t("user_unapproved_success") || "User has been unapproved successfully."
      );

      // Refresh the user list without full page loading
      await fetchUsers(false);

      // If we're in the modal, update the selected user
      if (selectedUser && selectedUser.id === userId) {
        setSelectedUser({
          ...selectedUser,
          is_approved: approve
        });
      }
    } catch (error: any) {
      console.error("Error approving/unapproving user:", error);

      // Use the error message from the API if available
      const errorMessage = error.message || (
        approve
          ? t("user_approved_error") || "Failed to approve user. Please try again."
          : t("user_unapproved_error") || "Failed to unapprove user. Please try again."
      );

      setApprovalError(errorMessage);

      // If it's a permission error, show a more specific message
      if (errorMessage.includes("Permission denied")) {
        setApprovalError(
          t("permission_denied") ||
          "Permission denied: You may not have sufficient permissions or this user cannot be modified."
        );
      }

      // If it's an authentication error, redirect to login
      if (errorMessage.includes("Authentication error")) {
        // Clear token and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userEmail');

        // Show message and redirect after a delay
        setApprovalError(t("session_expired") || "Your session has expired. Please log in again.");
        setTimeout(() => {
          router.push(`/${lng}/login`);
        }, 2000);
      }
    } finally {
      setIsApproving(false);

      // Clear success message after 3 seconds
      if (!approvalError) {
        setTimeout(() => {
          setApprovalSuccess("");
        }, 3000);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#52bcc3] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${embedded ? '' : 'min-h-screen bg-gray-50 p-6'}`}>
      {!embedded && (
        <div className="mb-6">
          <h1 className="text-2xl font-bold">User Management</h1>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {approvalSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {approvalSuccess}
        </div>
      )}

      {approvalError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {approvalError}
        </div>
      )}

      {/* Filters */}
      <div className={`bg-white shadow-md rounded-lg p-3 mb-4 ${embedded ? 'mt-0' : ''}`}>
        <div className="flex flex-wrap items-end gap-3">
          {/* Email filter */}
          <div className="flex-1 min-w-[150px] max-w-[250px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="text"
              value={filters.email || ""}
              onChange={(e) => handleFilterChange("email", e.target.value)}
              className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              placeholder="Search email"
            />
          </div>

          {/* Approval status filter */}
          <div className="w-[120px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.is_approved === undefined ? "" : filters.is_approved.toString()}
              onChange={(e) => {
                const value = e.target.value;
                handleFilterChange("is_approved", value === "" ? undefined : value === "true");
              }}
              className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
            >
              <option value="">{lng === "de" ? "Alle" : "All"}</option>
              <option value="true">{lng === "de" ? "Genehmigt" : "Approved"}</option>
              <option value="false">{lng === "de" ? "Ausstehend" : "Pending"}</option>
            </select>
          </div>

          {/* Role filter */}
          <div className="w-[120px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Role
            </label>
            <select
              value={filters.role || ""}
              onChange={(e) => handleFilterChange("role", e.target.value || undefined)}
              className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
            >
              <option value="">{lng === "de" ? "Alle" : "All"}</option>
              <option value={RoleEnum.USER}>{lng === "de" ? "Benutzer" : "User"}</option>
              <option value={RoleEnum.ADMIN}>{lng === "de" ? "Administrator" : "Admin"}</option>
            </select>
          </div>

          {/* Clear filters button */}
          <div className="ml-auto">
            <button
              onClick={clearFilters}
              className="px-3 py-1.5 text-sm bg-white border border-gray-400 text-gray-600 rounded-md hover:bg-gray-50 transition-colors"
            >
              {lng === "de" ? "Löschen" : "Clear"}
            </button>
          </div>
        </div>
      </div>

      {/* Users table */}
      <div className="bg-white shadow-lg rounded-lg overflow-hidden relative w-full">
        {/* Table loading overlay */}
        <div id="table-loading" className="hidden absolute inset-0 bg-white bg-opacity-70 z-10 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600 text-sm">Loading data...</p>
          </div>
        </div>

        <div className="overflow-x-auto w-full">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-blue-600 to-teal-500 text-white">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[5%]">
                  ID
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[20%]">
                  Email
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[15%]">
                  {lng === "de" ? "Name" : "Name"}
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[10%]">
                  {lng === "de" ? "Status" : "Status"}
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[10%]">
                  {lng === "de" ? "Rolle" : "Role"}
                </th>
                {/* Stage column - commented out as requested
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">
                  Stage
                </th>
                */}
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[15%]">
                  {lng === "de" ? "Verbleibende Zeit" : "Remaining Time"}
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[10%] cursor-pointer hover:bg-blue-700"
                  onClick={() => handleSort("created_at")}
                >
                  <div className="flex items-center">
                    {lng === "de" ? "Erstellt am" : "Created At"}
                    {filters.sort_by === "created_at" && (
                      <span className="ml-1">
                        {!filters.sort_desc ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider w-[15%]">
                  {lng === "de" ? "Aktionen" : "Actions"}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {!users || users.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-4 text-center text-gray-500">
                    {lng === "de" ? "Keine Benutzer gefunden" : "No users found"}
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user.id}>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {user.id}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {user.email}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {user.details ? (
                        <>
                          {user.details.first_name || ''} {user.details.last_name || ''}
                        </>
                      ) : (
                        // If no details available, show N/A
                        <span className="text-gray-500">N/A</span>
                      )}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-md ${
                        user.is_approved ? 'bg-green-500 text-white' : 'bg-yellow-500 text-white'
                      }`}>
                        {user.is_approved
                          ? t("user_status_approved")
                          : t("user_status_pending")}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-md ${
                        user.role === 'admin' ? 'bg-purple-500 text-white' : 'bg-blue-500 text-white'
                      }`}>
                        {user.role}
                      </span>
                    </td>
                    {/* Stage data - commented out as requested
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {typeof user.current_stage === 'string' ?
                        user.current_stage :
                        (user.current_stage === 1 ? 'signup' :
                         user.current_stage === 2 ? 'details' :
                         user.current_stage === 3 ? 'organization' :
                         user.current_stage === 4 ? 'avatar' :
                         user.current_stage === 5 ? 'complete' :
                         user.current_stage)}
                    </td>
                    */}
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {/* Display remaining time with progress bar */}
                      <div>
                        {typeof user.remaining_time === 'number' ? (
                          <>
                            <div className="font-medium text-gray-900">
                              {Math.floor(user.remaining_time / 60)}m {user.remaining_time % 60}s
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                              <div
                                className="bg-[#52bcc3] h-1.5 rounded-full"
                                style={{
                                  width: `${Math.min(100, (user.remaining_time / 900) * 100)}%`
                                }}
                              ></div>
                            </div>
                          </>
                        ) : (
                          <div className="font-medium text-gray-500">
                            N/A
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(user.created_at)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                      <div className="flex flex-wrap gap-2">
                        <button
                          onClick={() => handleViewUser(user)}
                          className="px-2 py-1 bg-white border border-blue-500 text-blue-500 rounded hover:bg-blue-50 transition-colors flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          {lng === "de" ? "Ansehen" : "View"}
                        </button>

                        {/* Email button removed as requested */}

                        {user.role !== 'admin' && (
                          <button
                            onClick={() => handleApproveUser(user.id, !user.is_approved)}
                            disabled={isApproving}
                            className={`px-2 py-1 rounded flex items-center ${
                              user.is_approved
                                ? 'bg-white border border-red-500 text-red-500 hover:bg-red-50'
                                : 'bg-white border border-green-500 text-black hover:bg-green-50'
                            } ${isApproving ? 'opacity-50 cursor-not-allowed' : ''}`}
                            title={String(user.role).toLowerCase() === 'admin'
                              ? (lng === "de" ? "Genehmigungsstatus von Administratoren kann nicht geändert werden" : "Cannot modify approval status of admin users")
                              : ""}
                          >
                            {user.is_approved ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                            {isApproving
                              ? "..."
                              : (user.is_approved
                                ? t("user_action_unapprove")
                                : t("user_action_approve"))}
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="bg-white px-4 py-4 flex items-center justify-between border-t border-gray-200">
          {/* Mobile pagination controls removed since we're showing all users */}
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                {totalUsers > 0 ? (
                  <>
                    Showing all{" "}
                    <span className="font-medium">{users.length}</span>{" "}
                    users
                  </>
                ) : (
                  <>
                    No users found
                  </>
                )}
              </p>
            </div>
            {/* Pagination controls removed since we're showing all users */}
          </div>
        </div>
      </div>

      {/* User Details Modal */}
      {isModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800">
                  User Details
                </h2>
                <button
                  onClick={handleCloseModal}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic User Information */}
                <div className="bg-gray-100 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">
                    Basic Information
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-gray-700">ID</p>
                      <p className="font-medium text-gray-900">{selectedUser.id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Email</p>
                      <p className="font-medium text-gray-900">{selectedUser.email}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Role</p>
                      <p className="font-medium">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-md ${
                          selectedUser.role === 'admin' ? 'bg-purple-600 text-white' : 'bg-blue-600 text-white'
                        }`}>
                          {selectedUser.role}
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Approval Status</p>
                      <p className="font-medium">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-md ${
                          selectedUser.is_approved ? 'bg-green-600 text-white' : 'bg-yellow-600 text-white'
                        }`}>
                          {selectedUser.is_approved ? t("user_status_approved") : t("user_status_pending")}
                        </span>
                      </p>
                    </div>
                    {/* Current Stage - commented out as requested
                    <div>
                      <p className="text-sm font-medium text-gray-700">Current Stage</p>
                      <p className="font-medium">
                        {typeof selectedUser.current_stage === 'string' ?
                          selectedUser.current_stage :
                          (selectedUser.current_stage === 1 ? 'Signup' :
                           selectedUser.current_stage === 2 ? 'Details' :
                           selectedUser.current_stage === 3 ? 'Organization' :
                           selectedUser.current_stage === 4 ? 'Avatar' :
                           selectedUser.current_stage === 5 ? 'Complete' :
                           selectedUser.current_stage)}
                      </p>
                    </div>
                    */}
                    <div>
                      <p className="text-sm font-medium text-gray-700">Created At</p>
                      <p className="font-medium text-gray-900">{formatDate(selectedUser.created_at)}</p>
                    </div>
                  </div>
                </div>

                {/* User Details */}
                <div className="bg-gray-100 p-4 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">
                    Personal Details
                  </h3>
                  {selectedUser.details ? (
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Name</p>
                        <p className="font-medium text-gray-900">
                          {selectedUser.details.first_name || ''} {selectedUser.details.last_name || ''}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Mobile Number</p>
                        <p className="font-medium text-gray-900">
                          {selectedUser.details.country_code} {selectedUser.details.mobile_number}
                        </p>
                      </div>
                      {/* Avatar Gender - commented out as requested
                      <div>
                        <p className="text-sm font-medium text-gray-700">Avatar Gender</p>
                        <p className="font-medium text-gray-900">{selectedUser.details.avatar_gender || 'N/A'}</p>
                      </div>
                      */}
                      <div>
                        <p className="text-sm font-medium text-gray-700">Date of Birth</p>
                        <p className="font-medium text-gray-900">{formatDate(selectedUser.details.birthdate)}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Organization</p>
                        <p className="font-medium text-gray-900">{selectedUser.details.org_name || 'N/A'}</p>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-700 font-medium">No details available</p>
                  )}
                </div>

                {/* Time Information */}
                <div className="bg-gray-100 p-4 rounded-lg border border-gray-200 md:col-span-2">
                  <h3 className="text-lg font-semibold mb-4 text-gray-800 border-b pb-2">
                    Time Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Remaining Time</p>
                      <p className="font-medium text-gray-900">
                        {typeof selectedUser.remaining_time === 'number' && selectedUser.remaining_time > 0
                          ? `${Math.floor(selectedUser.remaining_time / 60)}m ${selectedUser.remaining_time % 60}s`
                          : "N/A"}
                      </p>
                      {/* Progress bar for remaining time */}
                      {typeof selectedUser.remaining_time === 'number' && selectedUser.remaining_time > 0 && (
                        <div className="mt-2">
                          <div className="w-full bg-gray-300 rounded-full h-2.5">
                            <div
                              className="bg-[#52bcc3] h-2.5 rounded-full"
                              style={{
                                width: `${Math.min(100, (selectedUser.remaining_time / 900) * 100)}%`
                              }}
                            ></div>
                          </div>
                          <p className="text-xs font-medium text-gray-700 mt-1">
                            {Math.round((selectedUser.remaining_time / 900) * 100)}% of 15 minutes remaining
                          </p>
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Last Updated</p>
                      <p className="font-medium text-gray-900">{formatDate(selectedUser.created_at)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Success and error messages */}
              {approvalSuccess && (
                <div className="mt-4 p-3 bg-green-100 text-green-800 rounded-md">
                  {approvalSuccess}
                </div>
              )}

              {approvalError && (
                <div className="mt-4 p-3 bg-red-100 text-red-800 rounded-md">
                  {approvalError}
                </div>
              )}

              <div className="mt-6 flex flex-wrap justify-end gap-3">
                {/* Action buttons - removed token reset functionality */}
                <div className="flex-1">
                  {/* No additional action buttons needed for time-only management */}
                </div>

                {/* Close button */}
                <button
                  onClick={handleCloseModal}
                  className="px-4 py-2 bg-white border border-gray-400 text-gray-600 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>

                {/* Approve/Unapprove button */}
                {selectedUser.role !== 'admin' ? (
                  <button
                    onClick={() => handleApproveUser(selectedUser.id, !selectedUser.is_approved)}
                    disabled={isApproving}
                    className={`px-4 py-2 ${selectedUser.is_approved ? 'bg-white border border-red-500 text-red-500 hover:bg-red-50' : 'bg-white border border-green-500 text-green-500 hover:bg-green-50'} rounded-md transition-colors ${
                      isApproving ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {isApproving
                      ? "Processing..."
                      : (selectedUser.is_approved
                          ? t("user_action_unapprove")
                          : t("user_action_approve"))}
                  </button>
                ) : (
                  <button
                    disabled={true}
                    className="px-4 py-2 bg-white border border-gray-400 text-gray-400 rounded-md opacity-50 cursor-not-allowed"
                    title="Cannot modify approval status of admin users"
                  >
                    Admin User
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
