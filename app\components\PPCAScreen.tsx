"use client";

import { useState, useCallback, useEffect } from "react";
import { getTranslation } from "../i18n/translations";
import { setUserStage } from "../services/api";
import { useAuth } from "../context/ApiAuthContext";
import { signupService } from "../services/signupService";
import DynamicAvatar from "./AssistantAvatar";

// Component for personalization screen (third screen in the flow) added
export default function PersonalizationScreen({
  lng,
  avatarGender,
  onComplete
}: {
  lng: string;
  avatarGender: "male" | "female";
  onComplete: () => void;
}) {
  const { user } = useAuth();
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [firstName, setFirstName] = useState<string>("User");

  // Use the t function from our static translations
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Get user's first name
  useEffect(() => {
    if (user && 'first_name' in user) {
      setFirstName(user.first_name as string);
    } else if (user?.details && 'first_name' in user.details) {
      setFirstName(user.details.first_name as string);
    } else {
      const userDetails = signupService.getUserDetails();
      if (userDetails?.first_name) {
        setFirstName(userDetails.first_name);
      }
    }
  }, [user]);

  // Handle continue button click
  const handleContinue = useCallback(async () => {
    setIsSubmitting(true);
    setError("");

    try {
      // Update the user's stage to 3
      await setUserStage(3);

      // Call the onComplete callback to move to the next screen
      onComplete();
    } catch (error) {
      console.error('Failed to update stage:', error);
      setError(t("error_update_failed") || "Failed to update. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [onComplete]);

  // No need to calculate progress width anymore as it's handled by the ProgressBar component

  return (
    <div className="text-center max-w-xs sm:max-w-sm mx-auto px-2 sm:px-0">
      {/* Welcome text with updated message */}
      <div className="mb-4 sm:mb-6 text-base sm:text-lg font-semibold text-gray-800 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] bg-clip-text text-transparent">
        {t("hi_user").replace("{name}", firstName)}<br />
        {t("assistant_ready") || "Your personal assistant is ready!"}<br />
        {lng === "de" ? (
          <>
            Klicke auf <span className="font-bold">Gespräch beginnen</span>, um dich mit Luma zu
unterhalten.
          </>
        ) : (
          <>
            Click <span className="font-bold">Start conversation</span>  to begin your first conversation with your very personal Luma.
          </>
        )}
      </div>

      {error && (
        <div className="p-2 sm:p-3 mb-3 sm:mb-4 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 text-red-500 rounded-md text-xs sm:text-sm">
          {error}
        </div>
      )}

      <div className="flex flex-col justify-center items-center">
        {/* Avatar display */}
        <div className="flex justify-center items-center mb-3 sm:mb-4 relative w-full">
          <div className="relative mx-auto w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 lg:w-44 lg:h-44">
            <DynamicAvatar
              gender={avatarGender}
              isActive={true}
              audioLevel={0.3}
            />
          </div>
        </div>

        {/* Avatar type label */}
        <div className="mb-3 sm:mb-4 text-sm sm:text-base font-medium text-gray-800 bg-[#52bcc3]/10 py-1 sm:py-1.5 px-3 sm:px-4 rounded-full inline-block">
          {avatarGender === "female" ? t("female_avatar") || "Female Avatar" : t("male_avatar") || "Male Avatar"}
        </div>
      </div>

      <div className="mt-4 sm:mt-6">
        <button
          onClick={handleContinue}
          disabled={isSubmitting}
          className={`min-w-[160px] sm:min-w-[180px] py-2 sm:py-2.5 px-4 sm:px-6 ${
            isSubmitting
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer"
          } text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-3 w-3 sm:h-4 sm:w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t("processing") || "Processing..."}
            </span>
          ) : (
            <span className="font-bold">{t("start_conversation") || "Start Conversation"}</span>
          )}
        </button>
      </div>
    </div>
  );
}
