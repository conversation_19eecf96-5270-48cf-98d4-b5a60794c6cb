import AdminDashboardPage from "../../../components/AdminDashboardPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Admin Dashboard - Lumalife",
    description: "Manage your Lumalife admin settings"
  };
}

// Server component that passes the lng parameter to the client component
export default async function AdminDashboard({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;
  
  return <AdminDashboardPage lng={lng} />;
}
