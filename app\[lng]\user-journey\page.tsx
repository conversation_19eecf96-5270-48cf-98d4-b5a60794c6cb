import UserJourneyPage from "../../components/UserJourneyPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "LUMA LIFE",
    description: "Your personalized assistant journey"
  };
}

// Server component that passes the lng parameter to the client component
export default async function UserJourney({
  params,
}: {
  params: { lng: string };
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <UserJourneyPage lng={lng} />;
}
