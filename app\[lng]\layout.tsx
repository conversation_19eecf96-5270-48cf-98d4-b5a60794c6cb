import { ReactNode } from "react";
import AuthContext from "../context/AuthContext";
import { ApiAuthProvider } from "../context/ApiAuthContext";
import { LanguageProvider } from "../context/LanguageContext";
import { TransitionProvider } from "../context/TransitionContext";
import { BrowserCompatibilityProvider } from "../context/BrowserCompatibilityContext";
import GlobalLoading from "../components/GlobalLoading";
import BrowserCompatibilityNotification from "../components/BrowserCompatibilityNotification";
import "../globals.css";
import { dir } from "i18next";
import { languages } from "../i18n/settings";
import { <PERSON>ei<PERSON> } from "next/font/google";
import { Metadata } from "next";

// Optimize font loading by using only one font with minimal configuration
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "optional",
  preload: false,
  weight: ["400"],
  fallback: ["Arial", "sans-serif"],
  adjustFontFallback: true,
});

export async function generateStaticParams() {
  return languages.map((lng) => ({ lng }));
}

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Luma-Life",
    description: "Your personal assistant for healthier aging"
  };
}

export default async function LangLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: { lng: string };
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return (
    <html lang={lng} dir={dir(lng)}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        {/* Load environment variables from static file */}
        <script src="/static/env-config.js" />
      </head>
      <body className={`${geistSans.variable} antialiased`}>
        <GlobalLoading />
        <AuthContext>
          <LanguageProvider initialLanguage={lng}>
            <ApiAuthProvider>
              <BrowserCompatibilityProvider>
                <BrowserCompatibilityNotification position="top" showDismissButton={true} />
                <TransitionProvider>
                  {children}
                </TransitionProvider>
              </BrowserCompatibilityProvider>
            </ApiAuthProvider>
          </LanguageProvider>
        </AuthContext>
      </body>
    </html>
  );
}
