import PrivacyPage from "../../components/PrivacyPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Privacy Policy - Lumalife",
    description: "Privacy policy and data protection information for Lumalife"
  };
}

// Server component that passes the lng parameter to the client component
export default async function Privacy({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <PrivacyPage lng={lng} />;
}
