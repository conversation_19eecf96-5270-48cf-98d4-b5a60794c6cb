import AdminUserManagement from "../../../components/AdminUserManagement";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "User Management - Lumalife",
    description: "Manage users in the Lumalife admin panel"
  };
}

// Server component that passes the lng parameter to the client component
export default async function AdminUsers({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;
  
  return <AdminUserManagement lng={lng} />;
}
