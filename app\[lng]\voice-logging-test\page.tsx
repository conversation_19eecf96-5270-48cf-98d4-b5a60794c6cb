'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';

export default function VoiceLoggingTestPage() {
  const { user } = useAuth();
  const [logs, setLogs] = useState<any[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [status, setStatus] = useState<string>('Not initialized');

  useEffect(() => {
    // Initialize voice logging session when component mounts
    if (user) {
      initializeSession();
    }
  }, [user]);

  const initializeSession = async () => {
    try {
      const { initVoiceLoggingSession, getVoiceLogQueue } = await import('../../utils/voiceLogging');
      const newSessionId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      initVoiceLoggingSession(newSessionId, user?.id?.toString(), 'TEST');
      setSessionId(newSessionId);
      setStatus('Session initialized');
      
      // Update logs display
      const currentLogs = getVoiceLogQueue();
      setLogs(currentLogs);
    } catch (error) {
      console.error('Error initializing voice logging session:', error);
      setStatus('Error initializing session');
    }
  };

  const addTestLog = async () => {
    try {
      const { logVoiceMessage, getVoiceLogQueue } = await import('../../utils/voiceLogging');
      
      logVoiceMessage({
        speaker: 'user',
        timestamp: Date.now(),
        details: {
          messageType: 'test_message',
        },
        audioMetadata: {
          frequencyHz: 440,
          amplitudeDb: -20,
          duration: 1000,
          silenceDetected: false
        }
      });
      
      // Update logs display
      const currentLogs = getVoiceLogQueue();
      setLogs(currentLogs);
      setStatus('Test log added');
    } catch (error) {
      console.error('Error adding test log:', error);
      setStatus('Error adding test log');
    }
  };

  const sendLogs = async () => {
    try {
      const { triggerVoiceLogSend } = await import('../../utils/voiceLogging');
      
      setStatus('Sending logs...');
      const success = await triggerVoiceLogSend('manual_test');
      
      if (success) {
        setStatus('Logs sent successfully');
        // Clear logs display since they were sent
        setLogs([]);
      } else {
        setStatus('Failed to send logs');
      }
    } catch (error) {
      console.error('Error sending logs:', error);
      setStatus('Error sending logs');
    }
  };

  const endSession = async () => {
    try {
      const { endVoiceLoggingSession } = await import('../../utils/voiceLogging');
      
      await endVoiceLoggingSession('test_complete');
      setSessionId(null);
      setStatus('Session ended');
      setLogs([]);
    } catch (error) {
      console.error('Error ending session:', error);
      setStatus('Error ending session');
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Voice Logging Test</h1>
        <p>Please log in to test voice logging.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Voice Logging Test</h1>
      
      <div className="mb-4">
        <p><strong>User ID:</strong> {user.id}</p>
        <p><strong>Session ID:</strong> {sessionId || 'None'}</p>
        <p><strong>Status:</strong> {status}</p>
        <p><strong>Logs in Queue:</strong> {logs.length}</p>
      </div>

      <div className="space-x-2 mb-4">
        <button
          onClick={initializeSession}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Initialize Session
        </button>
        
        <button
          onClick={addTestLog}
          disabled={!sessionId}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:bg-gray-400"
        >
          Add Test Log
        </button>
        
        <button
          onClick={sendLogs}
          disabled={logs.length === 0}
          className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:bg-gray-400"
        >
          Send Logs
        </button>
        
        <button
          onClick={endSession}
          disabled={!sessionId}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:bg-gray-400"
        >
          End Session
        </button>
      </div>

      <div className="mt-6">
        <h2 className="text-xl font-semibold mb-2">Current Logs in Queue:</h2>
        <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <p>No logs in queue</p>
          ) : (
            <pre className="text-sm">
              {JSON.stringify(logs, null, 2)}
            </pre>
          )}
        </div>
      </div>

      <div className="mt-6">
        <h2 className="text-xl font-semibold mb-2">Instructions:</h2>
        <ol className="list-decimal list-inside space-y-1">
          <li>Click "Initialize Session" to start a voice logging session</li>
          <li>Click "Add Test Log" to add sample voice logs to the queue</li>
          <li>Click "Send Logs" to send the logs to the backend database</li>
          <li>Check the browser console and database to verify logs are being sent</li>
          <li>Click "End Session" to properly close the session</li>
        </ol>
      </div>
    </div>
  );
}
