"use client";

import { useEffect, useState, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";

export default function GlobalLoading() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const prevPathRef = useRef(pathname);
  const prevSearchParamsRef = useRef(searchParams);

  // Only trigger loading state for actual navigation, not just language changes
  useEffect(() => {
    // Check if this is a language change by comparing path structure
    const currentPath = pathname.split('/').slice(2).join('/');
    const prevPath = prevPathRef.current.split('/').slice(2).join('/');

    // Only show loading indicator for non-language switches
    if (currentPath !== prevPath || searchParams.toString() !== prevSearchParamsRef.current.toString()) {
      setIsLoading(true);

      // Use a shorter timeout for better UX
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 200);

      return () => clearTimeout(timer);
    }

    // Update refs for next comparison
    prevPathRef.current = pathname;
    prevSearchParamsRef.current = searchParams;
  }, [pathname, searchParams]);

  // Don't render anything if not loading
  if (!isLoading) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-1 z-50">
      <div className="h-full bg-[#52bcc3] animate-loading-bar"></div>
    </div>
  );
}
