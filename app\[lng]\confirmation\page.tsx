import ConfirmationPage from "../../components/SignUpPageConfirmationPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Confirmation - Lumalife",
    description: "Your registration is being processed"
  };
}

// Server component that passes the lng parameter to the client component
export default async function Confirmation({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <ConfirmationPage lng={lng} />;
}

