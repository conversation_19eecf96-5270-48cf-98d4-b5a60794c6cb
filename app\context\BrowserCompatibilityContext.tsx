"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

// Define the context type
interface BrowserCompatibilityContextType {
  isBrowserCompatible: boolean;
  hasMicrophonePermission: boolean;
  hasWebSocketSupport: boolean;
  hasAudioContextSupport: boolean;
  error: string | null;
  checkMicrophonePermission: () => Promise<boolean>;
  clearError: () => void;
}

// Create the context with default values
const BrowserCompatibilityContext = createContext<BrowserCompatibilityContextType>({
  isBrowserCompatible: true,
  hasMicrophonePermission: true,
  hasWebSocketSupport: true,
  hasAudioContextSupport: true,
  error: null,
  checkMicrophonePermission: async () => true,
  clearError: () => {},
});

// Provider component
export function BrowserCompatibilityProvider({ children }: { children: ReactNode }) {
  // State for browser compatibility
  const [isBrowserCompatible, setIsBrowserCompatible] = useState<boolean>(true);
  const [hasMicrophonePermission, setHasMicrophonePermission] = useState<boolean>(true);
  const [hasWebSocketSupport, setHasWebSocketSupport] = useState<boolean>(true);
  const [hasAudioContextSupport, setHasAudioContextSupport] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Check browser compatibility on mount
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    // Check for AudioContext support
    const hasAudioContext = typeof (window.AudioContext || (window as any).webkitAudioContext) !== 'undefined';
    setHasAudioContextSupport(hasAudioContext);

    // Check for getUserMedia support
    const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

    // Check for WebSocket support
    const hasWebSocket = typeof WebSocket !== 'undefined';
    setHasWebSocketSupport(hasWebSocket);

    // Set overall compatibility
    const isCompatible = hasAudioContext && hasGetUserMedia && hasWebSocket;
    setIsBrowserCompatible(isCompatible);

    // Set error message if not compatible
    if (!isCompatible) {
      let errorMessage = "Your browser doesn't support all required features. ";

      if (!hasAudioContext) errorMessage += "Audio playback is not supported. ";
      if (!hasGetUserMedia) errorMessage += "Microphone access is not supported. ";
      if (!hasWebSocket) errorMessage += "Real-time communication is not supported. ";

      setError(errorMessage + "You can still use the application, but some features may not work properly.");
    }


  }, []);

  // Function to check microphone permission
  const checkMicrophonePermission = async (): Promise<boolean> => {
    // Skip if browser doesn't support getUserMedia
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setHasMicrophonePermission(false);
      return false;
    }

    try {
      // Try to get microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // If successful, stop all tracks to release the microphone
      stream.getTracks().forEach(track => track.stop());

      // Clear any microphone-related errors
      if (error && error.includes("microphone")) {
        setError(null);
      }

      setHasMicrophonePermission(true);
      return true;
    } catch (err) {
      console.error('Microphone permission check failed:', err);

      // Set appropriate error message based on the error
      if (err instanceof DOMException) {
        if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
          setError("Microphone access was denied. Please allow microphone access in your browser settings to use voice features.");
        } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
          setError("No microphone found. Please connect a microphone to use voice features.");
        } else {
          setError(`Microphone error: ${err.message}. You can still use the application, but voice features may not work.`);
        }
      } else {
        setError("An error occurred while accessing the microphone. You can still use the application, but voice features may not work.");
      }

      setHasMicrophonePermission(false);
      return false;
    }
  };

  // Function to clear error
  const clearError = () => {
    setError(null);
  };

  // We no longer automatically check microphone permission on mount
  // Instead, components will explicitly call checkMicrophonePermission when needed

  return (
    <BrowserCompatibilityContext.Provider
      value={{
        isBrowserCompatible,
        hasMicrophonePermission,
        hasWebSocketSupport,
        hasAudioContextSupport,
        error,
        checkMicrophonePermission,
        clearError,
      }}
    >
      {children}
    </BrowserCompatibilityContext.Provider>
  );
}

// Hook to use the context
export function useBrowserCompatibility() {
  return useContext(BrowserCompatibilityContext);
}
