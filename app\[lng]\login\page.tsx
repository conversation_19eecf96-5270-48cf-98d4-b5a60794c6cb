import LoginPage from "../../components/LoginPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Login - Lumalife",
    description: "Log in to your Lumalife account - for both users and administrators"
  };
}

// Server component that passes the lng parameter to the client component
export default async function Login({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <LoginPage lng={lng} />;
}


