import { NextRequest, NextResponse } from 'next/server';

// API route to handle sendBeacon requests for time updates
// This endpoint receives POST requests from navigator.sendBeacon and forwards them to the backend
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    let requestData: any = {};

    try {
      // Try to parse JSON body first
      const body = await request.text();

      if (body && body.trim()) {
        requestData = JSON.parse(body);
      }
    } catch (parseError) {
      // If JSON parsing fails, try URL parameters
      const url = new URL(request.url);
      requestData = {
        remaining_time: url.searchParams.get('remaining_time'),
        userId: url.searchParams.get('userId'),
        timestamp: url.searchParams.get('timestamp'),
        source: url.searchParams.get('source'),
        token: url.searchParams.get('token')
      };
    }

    const { remaining_time, userId, token, source } = requestData;

    if (!remaining_time || !userId || !token) {
      // Return success for empty requests to avoid spam in logs
      if (!remaining_time && !userId && !token) {
        return NextResponse.json({ success: false, message: 'No data provided' }, { status: 200 });
      }
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    console.log(`Beacon update: User ${userId}, remaining time: ${remaining_time}s, source: ${source}`);

    // Forward the request to the backend API
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

    try {
      const backendResponse = await fetch(`${backendUrl}/users/me`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ remaining_time: parseInt(remaining_time) })
      });

      if (backendResponse.ok) {
        console.log(`Beacon update successful for user ${userId}`);
        return NextResponse.json({ success: true });
      } else {
        console.log(`Backend responded with status ${backendResponse.status}`);
        return NextResponse.json({ error: 'Backend update failed' }, { status: backendResponse.status });
      }
    } catch (backendError) {
      console.error('Error forwarding to backend:', backendError);
      return NextResponse.json({ error: 'Backend connection failed' }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in beacon update endpoint:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Also handle GET requests for URL parameter-based updates
export async function GET(request: NextRequest) {
  try {
    console.log('Beacon update GET endpoint called');

    const url = new URL(request.url);
    const remaining_time = url.searchParams.get('remaining_time');
    const userId = url.searchParams.get('userId');
    const token = url.searchParams.get('token');
    const source = url.searchParams.get('source');

    if (!remaining_time || !userId || !token) {
      console.log('Missing required parameters for beacon update');
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    console.log(`Beacon update GET: User ${userId}, remaining time: ${remaining_time}s, source: ${source}`);

    // Forward the request to the backend API
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

    try {
      const backendResponse = await fetch(`${backendUrl}/users/me`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ remaining_time: parseInt(remaining_time) })
      });

      if (backendResponse.ok) {
        console.log(`Beacon update GET successful for user ${userId}`);
        return NextResponse.json({ success: true });
      } else {
        console.log(`Backend responded with status ${backendResponse.status}`);
        return NextResponse.json({ error: 'Backend update failed' }, { status: backendResponse.status });
      }
    } catch (backendError) {
      console.error('Error forwarding to backend:', backendError);
      return NextResponse.json({ error: 'Backend connection failed' }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in beacon update GET endpoint:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
