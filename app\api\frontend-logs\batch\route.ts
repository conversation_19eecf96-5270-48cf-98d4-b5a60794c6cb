import { NextRequest, NextResponse } from 'next/server';

// Interface for voice log entries (matching the frontend interface)
interface VoiceLogEntry {
  speaker: 'user' | 'assistant';
  timestamp: number;
  audioMetadata?: {
    frequencyHz?: number;
    amplitudeDb?: number;
    duration?: number;
    sampleRate?: number;
    channels?: number;
    audioDataSize?: number;
    silenceDetected?: boolean;
    peakFrequency?: number;
    averageAmplitude?: number;
    rmsAmplitude?: number;
    maxAmplitude?: number;
    peakRMS?: number;
    playbackStartTime?: number;
    playbackEndTime?: number;
    playbackLatency?: number;
    actualPlaybackDuration?: number;
  };
  details?: {
    messageType?: string;
    latency?: number;
    error?: string;
    audioDataSize?: number;
    processingTime?: number;
    aggregatedChunks?: number;
    iphoneDebugging?: boolean;
    hasFrequencyData?: boolean;
    hasAmplitudeData?: boolean;
  };
  sessionId?: string;
  userId?: string;
  agentRole?: string;
  deviceEnvironment?: {
    device: string;
    browser: string;
    osVersion: string;
    outputDevice: string;
    networkType: string;
    userAgent?: string;
    screenResolution?: string;
    audioCapabilities?: {
      audioContext: boolean;
      webAudio: boolean;
      mediaDevices: boolean;
      audioWorklet: boolean;
    };
    permissions?: {
      microphone: string;
      notifications: string;
    };
    volumeLevel?: number;
    audioContextState?: string;
    autoplayPolicyBlocked?: boolean;
    audioOutputDevice?: string;
  };
}

// Interface for the batch request
interface VoiceLogBatchRequest {
  logs: VoiceLogEntry[];
  reason: string;
  timestamp: number;
  sessionId?: string;
  totalLogs: number;
}

/**
 * POST /api/frontend-logs/batch
 * Receives and processes batched voice conversation logs from the frontend
 */
export async function POST(request: NextRequest) {
  try {
    // Check request size (1MB limit for production safety)
    const contentLength = request.headers.get('content-length');
    const MAX_REQUEST_SIZE = 1024 * 1024; // 1MB

    if (contentLength && parseInt(contentLength) > MAX_REQUEST_SIZE) {
      return NextResponse.json({ error: 'Request too large' }, { status: 413 });
    }

    // Get the request body
    const body: VoiceLogBatchRequest = await request.json();
    const { logs, reason, timestamp, sessionId, totalLogs } = body;
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Missing authorization token' }, { status: 401 });
    }

    if (!logs || !Array.isArray(logs)) {
      return NextResponse.json({ error: 'Invalid logs data' }, { status: 400 });
    }

    // Additional validation for production safety
    if (logs.length > 1000) {
      return NextResponse.json({ error: 'Too many logs in batch' }, { status: 400 });
    }

    console.log(`Frontend logs batch received: ${totalLogs} logs, reason: ${reason}, session: ${sessionId || 'unknown'}`);

    // Process and validate logs
    const processedLogs = logs.map((log, index) => {
      // Validate required fields
      if (!log.speaker || !log.timestamp) {
        console.warn(`Invalid log entry at index ${index}:`, log);
        return null;
      }

      // Add processing timestamp and request metadata
      return {
        ...log,
        receivedAt: Date.now(),
        batchReason: reason,
        batchTimestamp: timestamp,
        logIndex: index
      };
    }).filter(log => log !== null); // Remove invalid logs

    // Log summary for debugging
    const logSummary = {
      totalReceived: totalLogs,
      validLogs: processedLogs.length,
      invalidLogs: totalLogs - processedLogs.length,
      reason,
      sessionId,
      speakers: {
        user: processedLogs.filter(log => log?.speaker === 'user').length,
        assistant: processedLogs.filter(log => log?.speaker === 'assistant').length
      },
      messageTypes: processedLogs.reduce((acc, log) => {
        const messageType = log?.details?.messageType || 'unknown';
        acc[messageType] = (acc[messageType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    console.log('Voice logs summary:', logSummary);

    // In a real implementation, you might want to:
    // 1. Store logs in a database
    // 2. Forward to a logging service
    // 3. Send to analytics platform
    // 4. Process for debugging insights

    // For now, we'll just log them and optionally forward to backend
    if (process.env.NODE_ENV === 'development') {
      // In development, log detailed information
      console.log('Detailed voice logs:', processedLogs.slice(0, 5)); // Log first 5 for debugging
    }

    // Always try to forward to backend logging service
    let backendForwardSuccess = false;
    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL ||
                        process.env.NEXT_PUBLIC_API_BASE_URL ||
                        process.env.API_URL ||
                        'https://api.lumalife.de';

      console.log(`Forwarding voice logs to backend: ${backendUrl}/frontend-logs/batch`);

      // Convert logs to the correct backend format
      const backendFormattedLogs = processedLogs.map(log => ({
        log_data: {
          level: log.details?.error ? 'error' : 'info',
          message: `${log.speaker} ${log.details?.messageType || 'voice_event'}`,
          component: `Assistant_${log.agentRole?.toUpperCase() || 'UNKNOWN'}`,
          timestamp: new Date(log.timestamp).toISOString(),
          url: request.headers.get('referer') || '',
          user_agent: request.headers.get('User-Agent') || '',
          // Voice-specific data
          speaker: log.speaker,
          session_id: log.sessionId,
          agent_role: log.agentRole,
          audio_metadata: log.audioMetadata,
          voice_details: log.details,
          device_environment: log.deviceEnvironment,
          user_id: log.userId
        },
        log_type: log.details?.error ? 'error' : 'voice'
      }));

      const backendResponse = await fetch(`${backendUrl}/frontend-logs/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          logs: backendFormattedLogs
        }),
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });

      if (backendResponse.ok) {
        console.log('Voice logs forwarded to backend successfully');
        backendForwardSuccess = true;
      } else {
        const responseText = await backendResponse.text().catch(() => 'Unable to read response');
        console.warn('Failed to forward voice logs to backend:', backendResponse.status, responseText);
      }
    } catch (backendError) {
      console.error('Error forwarding voice logs to backend:', backendError);
      // Don't fail the request if backend forwarding fails
    }

    // Return success response
    return NextResponse.json({
      success: true,
      processed: processedLogs.length,
      total: totalLogs,
      reason,
      sessionId,
      timestamp: Date.now(),
      backendForwarded: backendForwardSuccess
    });

  } catch (error) {
    console.error('Error processing voice logs batch:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    }, { status: 500 });
  }
}

/**
 * GET /api/frontend-logs/batch
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    endpoint: 'frontend-logs/batch',
    timestamp: Date.now(),
    methods: ['POST']
  });
}
