"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useTranslation } from "../i18n/client";
import { useLanguage } from "../context/LanguageContext";
import { useAuth } from "../context/ApiAuthContext";
import UserActionButtons from "./UserActionButtons";

// Client component that no longer needs lng parameter
export default function DashboardPage() {
  const router = useRouter();
  const { currentLanguage } = useLanguage();
  const { user, isLoading, isAuthenticated, isApproved } = useAuth();

  // Use language from user profile if available, otherwise use the language from context
  const userLanguage = user?.details?.language || currentLanguage;
  const { t } = useTranslation("common", userLanguage);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Redirect to login if not authenticated or to confirmation if not approved
  useEffect(() => {
    // Only redirect if we've checked authentication status
    if (!isLoading) {
      if (!isAuthenticated) {
        // Redirect to login if not authenticated
        router.push(`/${userLanguage}/login`);
      } else if (!isApproved) {
        // Redirect to login page if authenticated but not approved
        router.push(`/${userLanguage}/login`);
      }

      // After initial check, mark initial load as complete
      setIsInitialLoad(false);
    }
  }, [isLoading, isAuthenticated, isApproved, router, userLanguage]);

  // Prevent any redirections away from dashboard once we're authenticated
  useEffect(() => {
    // If we're authenticated, make sure we stay on the dashboard
    if (isAuthenticated) {
      // This will ensure we don't get redirected away from the dashboard
      const handleRouteChange = (url: string) => {
        if (!url.includes('/dashboard') && !url.includes('/logout')) {
          // We could cancel the navigation here if needed
          // But for now, we'll just monitor it
        }
      };

      // We could add a route change listener here if needed
      // window.addEventListener('beforeunload', handleBeforeUnload);
      // return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }
  }, [isAuthenticated]);

  // Simple loading state
  if (isLoading || isInitialLoad) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Fixed Header with UserActionButtons */}
        <div className="fixed top-0 left-0 right-0 bg-white shadow-lg border-b border-gray-100 z-40 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-20">
              <div className="flex items-center space-x-3">
                <img src="/images/logo_icon.png" alt="Logo" className="h-10 w-10" />
                <div>
                  <h1 className="text-xl font-bold text-gray-800">
                    {userLanguage === "de" ? "Benutzer Dashboard" : "User Dashboard"}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {userLanguage === "de" ? "Ihr Profil" : "Your Profile"}
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <UserActionButtons lng={userLanguage} variant="default" />
              </div>
            </div>
          </div>
        </div>

        {/* Loading content with top padding */}
        <div className="pt-20 min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#52bcc3] mx-auto mb-4"></div>
            <div className="text-gray-600 text-lg">{t("loading") || "Loading..."}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Header with UserActionButtons */}
      <div className="fixed top-0 left-0 right-0 bg-white shadow-lg border-b border-gray-100 z-40 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-3">
              <img src="/images/logo_icon.png" alt="Logo" className="h-10 w-10" />
              <div>
                <h1 className="text-xl font-bold text-gray-800">
                  {userLanguage === "de" ? "Benutzer Dashboard" : "User Dashboard"}
                </h1>
                <p className="text-sm text-gray-500">
                  {userLanguage === "de" ? "Ihr Profil" : "Your Profile"}
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <UserActionButtons lng={userLanguage} variant="default" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content with top padding to account for fixed header */}
      <div className="pt-20">
        <div className="py-12">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          {user && (
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                {userLanguage === "de" ? "Willkommen zurück!" : "Welcome Back!"}
              </h1>
              <p className="text-gray-600 mb-6">
                {userLanguage === "de"
                  ? `Hallo, ${user.details?.first_name || 'Benutzer'}!`
                  : `Hello, ${user.details?.first_name || 'User'}!`
                }
              </p>

              {/* Continue Journey Button */}
              <div className="flex justify-center">
                <button
                  onClick={() => router.push(`/${userLanguage}/user-journey`)}
                  className="group relative overflow-hidden bg-gradient-to-r from-[#3da8af] to-[#52bcc3] text-white rounded-xl px-8 py-4 hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
                >
                  <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                  <div className="relative flex items-center">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                      </svg>
                    </div>
                    <span className="font-bold text-lg">
                      {userLanguage === "de" ? "Benutzerreise fortsetzen" : "Continue User Journey"}
                    </span>
                  </div>
                </button>
              </div>
            </div>
          )}

          {/* Profile Card */}
          {user && (
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-8 relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#52bcc3]/10 to-[#3da8af]/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-[#3da8af]/10 to-[#52bcc3]/10 rounded-full translate-y-12 -translate-x-12"></div>

              <div className="relative">
                <div className="flex items-center mb-8">
                  <div className="w-20 h-20 bg-gradient-to-r from-[#3da8af] to-[#52bcc3] rounded-2xl flex items-center justify-center text-white text-3xl font-bold shadow-lg">
                    {user.details?.first_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                  </div>
                  <div className="ml-6">
                    <h2 className="text-2xl font-bold text-gray-800">
                      {user.details?.first_name} {user.details?.last_name}
                    </h2>
                    <p className="text-gray-600 text-lg">{user.email}</p>
                    <div className="flex items-center mt-2">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                        {user.is_approved ? (userLanguage === "de" ? "Aktiv" : "Active") : (userLanguage === "de" ? "Ausstehend" : "Pending")}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Personal Information */}
                <div className="bg-gray-50 rounded-xl p-6 space-y-4">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5 text-blue-600">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800">
                      {userLanguage === "de" ? "Persönliche Daten" : "Personal Information"}
                    </h4>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Vollständiger Name" : "Full Name"}
                    </label>
                    <p className="text-gray-800 font-medium">
                      {user.details?.first_name} {user.details?.last_name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "E-Mail" : "Email"}
                    </label>
                    <p className="text-gray-800 font-medium break-all">{user.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Telefon" : "Phone"}
                    </label>
                    <p className="text-gray-800 font-medium">
                      {user.details?.country_code} {user.details?.mobile_number}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Geburtsdatum" : "Birth Date"}
                    </label>
                    <p className="text-gray-800 font-medium">
                      {user.details?.birthdate ? new Date(user.details.birthdate).toLocaleDateString(userLanguage === "de" ? "de-DE" : "en-US") : (userLanguage === "de" ? "Nicht angegeben" : "Not specified")}
                    </p>
                  </div>
                </div>

                {/* Account Information */}
                <div className="bg-gray-50 rounded-xl p-6 space-y-4">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5 text-green-600">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800">
                      {userLanguage === "de" ? "Konto-Informationen" : "Account Information"}
                    </h4>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Organisation" : "Organization"}
                    </label>
                    <p className="text-gray-800 font-medium">
                      {user.details?.org_name === "alzheimer_schweiz"
                        ? (userLanguage === "de" ? "Alzheimer Schweiz" : "Alzheimer Switzerland")
                        : user.details?.org_name || (userLanguage === "de" ? "Nicht angegeben" : "Not specified")
                      }
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Sprache" : "Language"}
                    </label>
                    <p className="text-gray-800 font-medium">
                      {user.details?.language === "de" ? "Deutsch" : "English"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Registriert seit" : "Member Since"}
                    </label>
                    <p className="text-gray-800 font-medium">
                      {new Date(user.created_at).toLocaleDateString(userLanguage === "de" ? "de-DE" : "en-US")}
                    </p>
                  </div>
                </div>

                {/* Usage Statistics */}
                <div className="bg-gray-50 rounded-xl p-6 space-y-4">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5 text-purple-600">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-800">
                      {userLanguage === "de" ? "Nutzungsstatistiken" : "Usage Statistics"}
                    </h4>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Status" : "Status"}
                    </label>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {user.is_approved ? (userLanguage === "de" ? "Aktiv" : "Active") : (userLanguage === "de" ? "Ausstehend" : "Pending")}
                    </span>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      {userLanguage === "de" ? "Verbleibende Zeit" : "Remaining Time"}
                    </label>
                    <p className="text-gray-800 font-medium">
                      {user.remaining_time ?
                        (userLanguage === "de" ?
                          `${Math.floor(user.remaining_time / 60)} Min ${user.remaining_time % 60} Sek` :
                          `${Math.floor(user.remaining_time / 60)} min ${user.remaining_time % 60} sec`
                        ) :
                        (userLanguage === "de" ? "Nicht verfügbar" : "Not available")
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}


          </div>
        </div>
      </div>
    </div>
  );
}
