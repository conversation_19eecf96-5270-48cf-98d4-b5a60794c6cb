import SignUpPage from "../components/SignUpPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Welcome - Lumal<PERSON>",
    description: "Your personal assistant for healthier aging"
  };
}

// Server component that passes the lng parameter to the client component
export default async function Home({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <SignUpPage lng={lng} />;
}

