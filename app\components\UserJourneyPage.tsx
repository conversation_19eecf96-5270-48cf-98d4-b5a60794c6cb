"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../context/ApiAuthContext";
import { setUserStage } from "../services/api";
import WelcomePage from "./WelcomePage";
import AvatarSelection from "./AvatarSelection";
import ConversationPage from "./OnboardingAssistantScreen";
import PersonalizationScreen from "./PPCAScreen"; // Import for PPCA creation screen
import PPCAchat from "./PPCAchat"; // Import for PPCA conversation screen
import FeedbackScreen from "./FeedbackToken";
import FeedbackStartScreen from "./FeedbackStartScreen";
import FeedbackThankYouScreen from "./FeedbackAssistantScreen";
import FinalThankYouScreen from "./CompletionScreen";
import PageLayout from "./PageLayout";
import { STORAGE_KEYS } from "../config/appConfig";

// Define user stages enum to match backend UserStageEnum
export enum UserStage {
  BLOCKED = 0,           // User account is blocked/deactivated (not currently used)
  NEW = 1,               // Initial state after registration (Welcome screen)
  AVATAR_SELECTION = 2,  // User selecting their preferred avatar gender
  ONBOARDING = 3,        // User going through initial onboarding with OBA
  PPCA_CREATION = 4,     // User configuring their Personal Care Assistant
  CONVERSATION = 5,      // User in active conversation with PPCA
  FEEDBACK = 6,          // User providing feedback about experience
  REGULAR = 7            // User in normal usage state, all stages completed (Thank you screen)
}

export default function UserJourneyPage({ lng }: { lng: string }) {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: isAuthLoading } = useAuth();
  const [currentStage, setCurrentStage] = useState<UserStage | null>(null); // Start with null to prevent flashing
  const [selectedAvatar, setSelectedAvatar] = useState<"male" | "female">("female");
  const [isLoading, setIsLoading] = useState(true);
  const [authChecked, setAuthChecked] = useState(false);
  const [feedbackStep, setFeedbackStep] = useState<"initial" | "start" | "recording">("initial");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Check if user is authenticated, redirect to login if not
  useEffect(() => {
    // Track if we've already started a redirect to prevent multiple redirects
    let redirectInProgress = false;

    const checkAuth = async () => {
      try {
        // Keep loading state true until auth is fully checked
        setIsLoading(true);

        // Check if there's a token in localStorage
        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);

        // If no token, redirect to login immediately
        if (!token) {
          console.log('No token found, redirecting to login page');
          if (!redirectInProgress) {
            redirectInProgress = true;
            // Clear any stale data
            localStorage.removeItem('temp_auth_error');
            router.push(`/${lng}/login`);
          }
          return;
        }

        // If auth is still loading but we have a token, wait for it
        if (isAuthLoading && token) {
          return; // Don't set authChecked yet, wait for auth to finish loading
        }

        // If we have a token but not authenticated after auth has loaded, redirect to login
        if (!isAuthLoading && !isAuthenticated && token) {
          // Add a small delay before redirecting to prevent rapid loops
          if (!redirectInProgress) {
            redirectInProgress = true;

            // Clear the token to prevent redirect loops
            localStorage.removeItem(STORAGE_KEYS.TOKEN);

            // Set a temporary error message
            localStorage.setItem('temp_auth_error', 'Your session has expired. Please log in again.');

            // Redirect to login page after a short delay
            setTimeout(() => {
              router.push(`/${lng}/login`);
            }, 100);
          }
          return;
        }

        // If we're authenticated, check if user is admin first
        if (isAuthenticated && user) {

          // Check if user is admin - if so, redirect to admin dashboard
          const isAdmin = user.role === 'admin' ||
                         localStorage.getItem('userRole') === 'admin' ||
                         (user.email && user.email.toLowerCase().includes('admin'));

          if (isAdmin) {
            console.log('User is admin, redirecting to admin dashboard');

            // Store the role in localStorage for future reference
            localStorage.setItem('userRole', 'admin');

            // Check if we're already on the admin dashboard to prevent redirect loops
            if (!window.location.pathname.includes('/admin/dashboard')) {
              if (!redirectInProgress) {
                redirectInProgress = true;
                window.location.href = `/${lng}/admin/dashboard`;
              }
              return;
            } else {
              console.log('Already on admin dashboard, not redirecting');
              setAuthChecked(true);
              return;
            }
          }

          console.log('User is not admin, proceeding to user journey');
          setAuthChecked(true);
        } else if (isAuthenticated) {
          console.log('User is authenticated but user object not available yet, proceeding to user journey');
          setAuthChecked(true);
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        // If there's an error, redirect to login
        if (!redirectInProgress) {
          redirectInProgress = true;
          router.push(`/${lng}/login`);
        }
      }
    };

    checkAuth();
  }, [isAuthenticated, isAuthLoading, user, router, lng]);

  // Determine the current stage based on user data
  useEffect(() => {
    // Only proceed if auth has been checked and we're authenticated
    if (!authChecked || !isAuthenticated) {
      return;
    }

    setIsLoading(true);

    if (user) {
      // Check if user has a current_stage set
      if (user.current_stage !== undefined && user.current_stage !== null) {
        // If user has an avatar gender set, they've completed avatar selection
        if (user.details?.avatar_gender) {
          // For existing users who have already selected an avatar, use their selection
          setSelectedAvatar(user.details.avatar_gender as "male" | "female");

          // Determine which screen to show based on user's current_stage
          if (user.current_stage >= 1 && user.current_stage <= 7) {
            // Map backend stage directly to frontend stage
            setCurrentStage(user.current_stage as UserStage);

            // Reset feedback step when stage changes
            setFeedbackStep("initial");
          } else {
            // Default to NEW for any other stage
            setCurrentStage(UserStage.NEW);
          }
        } else {
          // No avatar selected yet - ensure female avatar is shown initially
          setSelectedAvatar("female");

          if (user.current_stage >= 3) {
            // If stage is 3 or higher but no avatar, force back to avatar selection
            setCurrentStage(UserStage.AVATAR_SELECTION);
          } else if (user.current_stage === 2) {
            // If stage is 2, show avatar selection
            setCurrentStage(UserStage.AVATAR_SELECTION);
          } else {
            // If stage is 1 or less, show welcome page
            setCurrentStage(UserStage.NEW);
          }
        }
      } else {
        // No current_stage set, default to NEW
        setCurrentStage(UserStage.NEW);
      }
    } else {
      // No user data available yet, but we're authenticated
      // This is a strange state, but we'll default to NEW
      setCurrentStage(UserStage.NEW);
    }

    setIsLoading(false);
  }, [user, authChecked, isAuthenticated]);

  // Handle avatar selection completion
  const handleAvatarSelected = async (avatarGender: "male" | "female") => {
    try {
      setSelectedAvatar(avatarGender);
      await setUserStage(3);
      setCurrentStage(UserStage.ONBOARDING);
    } catch (error) {
      console.error('Failed during avatar selection:', error);
    }
  };

  // Handle change avatar request from conversation page
  const handleChangeAvatar = () => {
    setCurrentStage(UserStage.AVATAR_SELECTION);
  };

  // Handle conversation completion
  const handleConversationComplete = () => {
    setCurrentStage(UserStage.PPCA_CREATION);
  };

  // Handle personalization completion
  const handlePersonalizationComplete = async () => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      await setUserStage(5);
      setCurrentStage(UserStage.CONVERSATION);
    } catch (error) {
      console.error('Failed during personalization completion:', error);
      setSubmitError('Failed to update stage. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle preferences completion
  const handlePreferencesComplete = async () => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      await setUserStage(6);
      setCurrentStage(UserStage.FEEDBACK);
    } catch (error) {
      console.error('Failed to update stage:', error);
      setSubmitError('Failed to update stage. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle "Give Feedback" button click (redirects to feedback start screen)
  const handleGiveFeedbackClick = () => {
    // Show the feedback start screen
    setFeedbackStep("start");
  };

  // Handle "Start Conversation" button click (redirects to feedback recording screen)
  const handleStartConversationClick = () => {
    // Show the feedback recording screen
    setFeedbackStep("recording");
  };

  // Handle "Submit Feedback" button click
  const handleSubmitFeedbackClick = async () => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      // Update backend stage to 7 when submit feedback is clicked
      await setUserStage(7);
      // Go to thank you screen
      setCurrentStage(UserStage.REGULAR);
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      setSubmitError('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle welcome page completion
  const handleWelcomeComplete = () => {
    setCurrentStage(UserStage.AVATAR_SELECTION);
  };

  // Show loading indicator while determining which screen to show
  if (isLoading || !authChecked || currentStage === null) {
    return (
      <div className="min-h-screen flex items-center justify-center px-4">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-t-2 border-b-2 border-[#52bcc3] mb-3 sm:mb-4"></div>
          <p className="text-gray-600 text-xs sm:text-sm">Loading your journey...</p>
        </div>
      </div>
    );
  }

  // Render the persistent layout with progress bar and conditionally render the appropriate component
  return (
    <PageLayout
      currentStage={currentStage as UserStage} // Type assertion since we've checked it's not null
      feedbackStep={feedbackStep}
      containerStyle="rounded"
      showActionButtons={true} // Show action buttons on all stages
      actionButtonsVariant={currentStage === UserStage.REGULAR ? "white" : "default"}
    >
      {/* Conditionally render the appropriate component based on the current stage */}
            {currentStage === UserStage.NEW && (
              <WelcomePage
                lng={lng}
                onComplete={handleWelcomeComplete}
              />
            )}

            {currentStage === UserStage.AVATAR_SELECTION && (
              <AvatarSelection
                lng={lng}
                initialAvatar={selectedAvatar}
                onAvatarSelected={handleAvatarSelected}
              />
            )}

            {currentStage === UserStage.ONBOARDING && (
              <ConversationPage
                lng={lng}
                avatarGender={selectedAvatar}
                onComplete={handleConversationComplete}
              />
            )}

            {currentStage === UserStage.PPCA_CREATION && (
              <PersonalizationScreen
                lng={lng}
                avatarGender={selectedAvatar}
                onComplete={handlePersonalizationComplete}
              />
            )}

            {currentStage === UserStage.CONVERSATION && (
              <PPCAchat
                lng={lng}
                avatarGender={selectedAvatar}
                onComplete={handlePreferencesComplete}
              />
            )}

            {currentStage === UserStage.FEEDBACK && feedbackStep === "initial" && (
              <FeedbackScreen
                lng={lng}
                avatarGender={selectedAvatar}
                onComplete={handleGiveFeedbackClick}
              />
            )}

            {currentStage === UserStage.FEEDBACK && feedbackStep === "start" && (
              <FeedbackStartScreen
                lng={lng}
                avatarGender={selectedAvatar}
                onComplete={handleStartConversationClick}
              />
            )}

            {currentStage === UserStage.FEEDBACK && feedbackStep === "recording" && (
              <FeedbackThankYouScreen
                lng={lng}
                avatarGender={selectedAvatar}
                onComplete={handleSubmitFeedbackClick}
              />
            )}

            {currentStage === UserStage.REGULAR && (
              <FinalThankYouScreen
                lng={lng}
              />
            )}
    </PageLayout>
  );
}
