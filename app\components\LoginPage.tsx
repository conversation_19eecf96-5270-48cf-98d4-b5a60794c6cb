"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import { getTranslation } from "../i18n/translations";
import { useAuth } from "../context/ApiAuthContext";
import { STORAGE_KEYS } from "../config/appConfig";

// Client component that receives the lng parameter directly
export default function LoginPage({ lng }: { lng: string }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { login, user, isAuthenticated, isApproved, error: authError } = useAuth();
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Helper function to get translation
  const t = (key: string) => getTranslation(key, lng);

  // Clear any temporary auth errors and check for token on mount
  useEffect(() => {
    // Check for temporary auth error and set it if present
    const tempAuthError = localStorage.getItem('temp_auth_error');
    if (tempAuthError) {
      // Found temporary auth error on page load

      // Check if the error message indicates approval issue
      if (tempAuthError.includes('awaiting approval') ||
          tempAuthError.includes('administrator approval') ||
          tempAuthError.includes('not approved') ||
          tempAuthError.includes('pending approval')) {
        // Setting approval error from localStorage
        setError(t("account_not_approved"));
      } else {
        // Use the error from localStorage
        setError(tempAuthError);
      }

      // Clear the temporary error
      localStorage.removeItem('temp_auth_error');
    }

    // Don't immediately redirect based only on token existence
    // We'll let the auth context handle this in the secondary check
    // This prevents redirection loops when token exists but is invalid
  }, [lng, t]);

  // Secondary check that uses the auth context
  useEffect(() => {
    if (isRedirecting) return;

    const checkTokenAndRedirect = async () => {
      try {
        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);

        // Only proceed if we have a token and auth state is fully loaded
        if (token && !isLoading) {
          // If user is authenticated, redirect to user journey
          if (isAuthenticated) {
            console.log('User is authenticated, redirecting to user journey');
            setIsRedirecting(true);
            window.location.href = `/${lng}/user-journey`;
            return;
          }

          // If we have a token but auth state says not authenticated,
          // the token is likely invalid - clear it to prevent redirect loops
          if (!isAuthenticated) {
            console.log('Token exists but user is not authenticated - token may be invalid');
            // Don't clear token here - let the API service handle that
            // This prevents redirect loops
          }
        }
      } catch (error) {
        console.error('Error checking token validity:', error);
      }
    };

    checkTokenAndRedirect();
  }, [isAuthenticated, isLoading, lng, isRedirecting]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim() || !password.trim()) {
      setError(t("all_fields_required"));
      return;
    }

    setError("");
    setIsLoading(true);

    try {
      // Call the login function from auth context
      const loginSuccess = await login(email, password);

      // If login failed, display the appropriate error
      if (!loginSuccess) {
        // Check for temporary auth error in localStorage first
        const tempAuthError = localStorage.getItem('temp_auth_error');

        if (tempAuthError) {
          // Found temporary auth error

          // Check if the error message indicates approval issue
          if (tempAuthError.includes('awaiting approval') ||
              tempAuthError.includes('administrator approval') ||
              tempAuthError.includes('not approved') ||
              tempAuthError.includes('pending approval')) {
            // User has valid credentials but is not approved
            setError(t("account_not_approved"));
          } else {
            // Use the error from localStorage
            setError(tempAuthError);
          }

          // Clear the temporary error
          localStorage.removeItem('temp_auth_error');
        }
        // If there's an auth error from the context, use it
        else if (authError) {
          console.log('Auth error from context:', authError);

          // Check if the error message indicates approval issue
          if (authError.includes('awaiting approval') ||
              authError.includes('administrator approval') ||
              authError.includes('not approved') ||
              authError.includes('pending approval')) {
            console.log('User has valid credentials but is not approved');
            setError(t("account_not_approved"));
          } else {
            // Use the error from auth context
            setError(authError);
          }
        } else {
          // Default to invalid credentials error
          console.log('No specific error, using invalid credentials message');
          setError(t("error_invalid_credentials"));
        }

        setIsLoading(false);
        return;
      }

      // If we're already redirecting, don't continue
      if (isRedirecting) {
        return;
      }

      // If login was successful, handle redirection
      // Login successful, checking user status

      // Double check if the user is approved (this should rarely be needed since the login function should have caught it)
      if (isAuthenticated && !isApproved) {
        // User is authenticated but not approved

        // Store the error in localStorage to ensure it persists
        const approvalErrorMsg = t("account_not_approved");
        localStorage.setItem('temp_auth_error', approvalErrorMsg);

        setError(approvalErrorMsg);
        setIsLoading(false);
        return;
      }

      // Determine if user is admin
      const userRole = user?.role || localStorage.getItem('userRole');
      const isAdmin = userRole === 'admin';

      // User role and admin status determined

      // Redirect based on role
      setIsRedirecting(true);
      if (isAdmin) {
        window.location.href = `/${lng}/admin/dashboard`;
      } else {
        window.location.href = `/${lng}/user-journey`;
      }
    } catch (err) {
      setError(t("error_generic"));
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-white">
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md p-6 sm:p-8 md:p-10 space-y-6 sm:space-y-8 bg-white border border-gray-200 rounded-2xl sm:rounded-3xl shadow-md">
        <div className="text-center">
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-10 sm:h-12 md:h-14 w-auto rounded-xl overflow-hidden "
            />
          </div>

          <h2 className="text-lg sm:text-xl font-medium text-gray-800 mb-4 sm:mb-6">
            {lng === "de" ? "Anmelden" : "Login"}
          </h2>

          <p className="mb-6 sm:mb-8 text-gray-700 px-2 sm:px-4 text-sm sm:text-base">
            {lng === "de"
              ? "Willkommen zurück! Bitte melden Sie sich an, um fortzufahren. Administratoren werden automatisch zum Admin-Dashboard weitergeleitet."
              : "Welcome back! Please log in to continue. Administrators will be automatically redirected to the admin dashboard."}
          </p>

          {error && (
            <div className={`p-2 sm:p-3 mb-3 sm:mb-4 ${
              typeof error === 'string' && error.includes('database connection error')
                ? 'bg-yellow-500/10 border border-yellow-500/30 text-yellow-700'
                : typeof error === 'string' && (
                    error.includes('awaiting approval') ||
                    error.includes('administrator approval') ||
                    error.includes('Thank you for registering') ||
                    error.includes('account is pending approval') ||
                    error.includes('account has not been approved') ||
                    error.includes('not approved') ||
                    error === t("account_not_approved")
                  )
                  ? 'bg-blue-500/10 border border-blue-500/30 text-blue-700'
                  : typeof error === 'string' && (
                    error.includes('Invalid email or password') ||
                    error.includes('Invalid credentials') ||
                    error === t("error_invalid_credentials")
                  )
                  ? 'bg-red-500/10 border border-red-500/30 text-red-500'
                  : 'bg-red-500/10 border border-red-500/30 text-red-500'
            } rounded-md text-xs sm:text-sm`}>
              {error}
            </div>
          )}

          <form onSubmit={handleLogin} className="space-y-2 sm:space-y-3">
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={lng === "de" ? "E-Mail*" : "Email*"}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-400 rounded-full text-sm sm:text-base placeholder-gray-500"
                required
              />
            </div>

            <div>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder={lng === "de" ? "Passwort*" : "Password*"}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-400 rounded-full text-sm sm:text-base placeholder-gray-500"
                required
              />
            </div>

            <div className="text-right  ">
              <Link href={`/${lng}/forgot-password`} className="text-[#52bcc3] hover:underline text-xs sm:text-sm">
                {lng === "de" ? "Passwort vergessen?" : "Forgot password?"}
              </Link>
            </div>

            <div className="pt-1 sm:pt-1">
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full py-2 sm:py-3 px-4 sm:px-6 text-white text-sm sm:text-base rounded-full font-medium transition-colors ${
                  isLoading
                    ? 'bg-gray-400 cursor-not-allowed opacity-70'
                    : 'bg-[#52bcc3] hover:bg-[#3da8af]'
                }`}
              >
                {isLoading
                  ? (lng === "de" ? "Anmeldung..." : "Signing in...")
                  : (lng === "de" ? "Anmelden" : "Login")}
              </button>
            </div>

            <div className="text-center text-xs sm:text-sm text-gray-700 mt-3 sm:mt-4">
              {lng === "de" ? "Noch kein Konto?" : "Don't have an account?"} <Link href={`/${lng}/signup`} className="text-[#52bcc3] hover:underline">{lng === "de" ? "Registrieren" : "Sign up"}</Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
