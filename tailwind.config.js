/** @type {import('tailwindcss').Config} */
module.exports = {
  // Optimize content scanning for better performance
  content: [
    './app/**/*.{js,ts,jsx,tsx}', // Removed mdx as it's not used
  ],
  // Optimize theme for better performance
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)', 'system-ui', 'sans-serif'],
        // Removed mono font family as it's not needed
      },
    },
  },
  // No plugins for better performance
  plugins: [],
  // Optimize for development and performance
  future: {
    hoverOnlyWhenSupported: true,
    disableColorOpacityUtilitiesByDefault: true, // Reduce CSS size
    respectDefaultRingColorOpacity: true, // Better defaults
  },
  // Ensure CSS is properly generated in production
  safelist: [
    // Add critical classes that might be dynamically generated
    'antialiased',
    'bg-white',
    'text-gray-800',
    'rounded-full',
    'bg-[#52bcc3]',
    'text-white',
    'hover:bg-[#3da8af]',
    'transition-colors'
  ],
  // Disable all variants for better performance
  corePlugins: {
    // Disable rarely used plugins
    container: false,
    animation: false,
    backdropBlur: false,
    backdropBrightness: false,
    backdropContrast: false,
    backdropGrayscale: false,
    backdropHueRotate: false,
    backdropInvert: false,
    backdropOpacity: false,
    backdropSaturate: false,
    backdropSepia: false,
    backgroundBlendMode: false,
    gradientColorStops: false,
  },
}
