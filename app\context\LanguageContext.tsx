"use client";

import { createContext, use<PERSON>ontext, ReactNode, useMemo, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";
import { languages } from "../i18n/settings";
import { updateUserLanguage } from "../services/api";
import { useAuth } from "./ApiAuthContext";

type LanguageContextType = {
  currentLanguage: string;
  changeLanguage: (newLang: string) => void;
};

const LanguageContext = createContext<LanguageContextType>({
  currentLanguage: "en",
  changeLanguage: () => {}
});

export function LanguageProvider({
  children,
  initialLanguage
}: {
  children: ReactNode;
  initialLanguage: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, refreshUserProfile } = useAuth();

  // Memoize the changeLanguage function to prevent unnecessary re-renders
  const changeLanguage = useCallback(async (newLang: string) => {
    if (newLang === initialLanguage || !languages.includes(newLang)) return;

    // Use regex for more reliable path extraction
    const pathWithoutLang = pathname.replace(/^\/[a-z]{2}(?:\/|$)/, '/');

    // If user is authenticated, update their language preference in the backend
    if (isAuthenticated) {
      try {
        // Update language in the backend and wait for it to complete
        console.log('Updating user language preference to:', newLang);
        await updateUserLanguage(newLang);

        // Force refresh the user profile to get the updated language
        try {
          const updatedProfile = await refreshUserProfile();
          console.log('User profile refreshed with new language:', updatedProfile?.details?.language);
        } catch (refreshError) {
          console.error('Failed to refresh user profile after language update:', refreshError);
        }
      } catch (error) {
        console.error('Failed to update user language preference:', error);
        // Continue with navigation even if the API call fails
      }
    }

    // Navigate to the same page with new language
    router.push(`/${newLang}${pathWithoutLang === '/' ? '' : pathWithoutLang}`);
  }, [initialLanguage, pathname, router, isAuthenticated]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    currentLanguage: initialLanguage,
    changeLanguage
  }), [initialLanguage, changeLanguage]);

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
}

// Optimized hook for accessing language context
export function useLanguage() {
  return useContext(LanguageContext);
}
