"use client";

import { create<PERSON>ontext, use<PERSON>ontext, use<PERSON>emo, ReactNode } from "react";

// Simplified context with no state management
interface TransitionContextType {
  isTransitioning: boolean;
}

// Create context with default value
const TransitionContext = createContext<TransitionContextType>({
  isTransitioning: false,
});

// Simplified provider that doesn't track route changes
export function TransitionProvider({ children }: { children: ReactNode }) {
  // Use a static value to avoid unnecessary re-renders
  const contextValue = useMemo(() => ({
    isTransitioning: false,
  }), []);

  return (
    <TransitionContext.Provider value={contextValue}>
      {children}
    </TransitionContext.Provider>
  );
}

// Hook to access transition context
export function useTransition() {
  return useContext(TransitionContext);
}
