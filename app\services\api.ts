// API Service for handling all backend requests
import { API_BASE_URL } from '../config/appConfig';
const BASE_URL = API_BASE_URL;

export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    SIGNUP: '/auth/signup',
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    CHANGE_PASSWORD: '/auth/change-password',
  },
  // User endpoints
  USERS: {
    ME: '/users/me',
  },
  // Journey endpoints
  JOURNEY: {
    SET_STAGE: '/journey/set-stage',
  },
  // Assistant endpoints
  ASSISTANTS: {
    STATUS: '/assistants/status',
  },
  // Admin endpoints
  ADMIN: {
    USERS: {
      LIST: '/admin/users',
      PENDING_APPROVAL: '/admin/users/pending-approval',
      STATS: '/admin/users/stats',
      BULK_APPROVE: '/admin/users/bulk-approve',
      APPROVE_USER: (userId: number) => `/admin/users/${userId}/approve`,
    }
  }
};

// Types
export interface LoginRequest {
  username: string;  // This is actually the email
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  role?: string; // Added role field to identify admin vs user
}

export interface UserDetails {
  first_name: string;
  last_name: string;
  country_code: string;
  mobile_number: string;
  avatar_gender: string; // This is required by the backend
  birthdate: string;
  org_name?: string;
  language?: string; // User's preferred language (e.g., 'en', 'de')
}

export interface SignupRequest {
  email: string;
  password: string;
  details: UserDetails;
}



export interface UserProfile {
  id: number;
  email: string;
  is_approved: boolean;
  role: string;
  current_stage: number;
  created_at: string;
  max_token_limit: number;
  tokens_used: number;
  tokens_remaining: number;
  remaining_time?: number; // Added remaining_time field
  details: UserDetails;
}

export interface ResetPasswordRequest {
  token: string;
  new_password: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface ApiError {
  detail: string;
  status_code?: number;
  error_type?: string;
}

// Common status codes:
// 400 - Bad Request (invalid input)
// 401 - Unauthorized (invalid/expired token)
// 403 - Forbidden (not approved/insufficient permissions)
// 404 - Not Found
// 422 - Validation Error

// API Functions
export async function login(email: string, password: string): Promise<LoginResponse> {
  try {
    // Check if there's an "Email already registered" error in localStorage
    const tempError = localStorage.getItem('temp_auth_error');
    if (tempError && tempError.includes('Email already registered')) {
      // Email already registered error detected
      // Return a rejected promise instead of throwing an error
      return Promise.reject({
        status_code: 400,
        detail: 'Email already registered. Please use a different email address or login with your existing account.',
        error_type: 'EMAIL_ALREADY_REGISTERED'
      });
    }

    console.log('Login attempt for email:', email);
    console.time('login_api_call'); // Add timing for performance measurement

    // Create form data for login request
    const formData = new FormData();
    formData.append('username', email);    // Note: API expects 'username' but uses email
    formData.append('password', password);

    console.log('Sending login request to:', `${BASE_URL}${API_ENDPOINTS.AUTH.LOGIN}`);

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutDuration = 15000; // 15 second timeout
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

    try {
      // Make the login request with timeout
      const response = await fetch(`${BASE_URL}${API_ENDPOINTS.AUTH.LOGIN}`, {
        method: 'POST',
        body: formData,
        signal: controller.signal
      });

      console.timeEnd('login_api_call'); // End timing

      // Handle error responses
      if (!response.ok) {
        // Get the response text
        const responseText = await response.text();

        // Create error message based on status code
        let errorMessage = 'Login failed';
        let errorType = 'UNKNOWN_ERROR';

        if (response.status === 401 || response.status === 403) {
          errorMessage = 'Invalid email or password';
          errorType = 'INVALID_CREDENTIALS';

          console.log('Invalid credentials detected with status:', response.status);

          // For invalid credentials, return a special error object instead of throwing
          // This prevents the page from refreshing
          return Promise.reject({
            status_code: response.status,
            detail: errorMessage,
            error_type: errorType
          });
        } else if (response.status === 500) {
          errorMessage = 'Server error. Please try again later.';
          errorType = 'SERVER_ERROR';
        }

        // Try to parse error details from response
        try {
          const errorData = JSON.parse(responseText);
          if (errorData && errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (e) {
          // If parsing fails, use the default message
        }

        // Create and throw error object
        const apiError: ApiError = {
          status_code: response.status,
          detail: errorMessage,
          error_type: errorType
        };

        throw apiError;
      }

      // Handle successful response
      console.log('Login successful, parsing response');
      console.time('login_parse_response'); // Add timing for response parsing

      // Get the response text
      const responseText = await response.text();

      if (!responseText || responseText.trim() === '') {
        console.error('Empty response from server');
        throw new Error('Empty response from server');
      }

      // Parse the response JSON
      const data = JSON.parse(responseText) as LoginResponse;
      console.timeEnd('login_parse_response'); // End timing for response parsing

      if (!data.access_token) {
        console.error('No access token in response');
        throw new Error('No access token in response');
      }

      // Store the token and return the response
      console.log('Token received and stored');
      localStorage.setItem('token', data.access_token);

      // Check if the response includes a role
      if (data.role) {
        console.log('Role found in login response:', data.role);
        localStorage.setItem('userRole', data.role);
      } else {
        // Try to get the user profile to determine the role
        console.time('profile_fetch'); // Add timing for profile fetch
        console.log('Fetching user profile to determine role');

        // Make a request to get the user profile with timeout
        const profileController = new AbortController();
        const profileTimeoutDuration = 10000; // 10 second timeout
        const profileTimeoutId = setTimeout(() => profileController.abort(), profileTimeoutDuration);

        try {
          const profileResponse = await fetch(`${BASE_URL}${API_ENDPOINTS.USERS.ME}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${data.access_token}`
            },
            signal: profileController.signal
          });

          console.timeEnd('profile_fetch'); // End timing for profile fetch

          if (profileResponse.ok) {
            console.time('profile_parse'); // Add timing for profile parsing
            const profileData = await profileResponse.json();
            console.timeEnd('profile_parse'); // End timing for profile parsing
            console.log('User profile data fetched successfully');

            // Check if the user is an admin
            if (profileData && profileData.role) {
              console.log('User role from profile:', profileData.role);
              localStorage.setItem('userRole', profileData.role);

              // Add role to the response
              data.role = profileData.role;
            }
          }

          // Check if there's a user-specific backup remaining time in localStorage
          // First get the user profile to get the user ID for backup key
          try {
            const profileResponse = await fetch(`${BASE_URL}${API_ENDPOINTS.USERS.ME}`, {
              headers: {
                'Authorization': `Bearer ${data.access_token}`
              }
            });

            if (profileResponse.ok) {
              const userProfile = await profileResponse.json();
              const userId = userProfile.id || userProfile.user_id;
              const currentApiTime = userProfile.remaining_time || 0;

              if (userId) {
                console.log(`Current API remaining time for user ${userId}: ${currentApiTime} seconds`);

                // Use user-specific backup key to prevent cross-user contamination
                const userBackupKey = `backup_remaining_time_user_${userId}`;
                const backupRemainingTime = localStorage.getItem(userBackupKey);

                if (backupRemainingTime) {
                  const backupTimeInSeconds = parseInt(backupRemainingTime, 10);
                  console.log(`Found user-specific backup remaining time in localStorage for user ${userId}: ${backupTimeInSeconds} seconds`);

                  if (!isNaN(backupTimeInSeconds) && backupTimeInSeconds >= 0) {
                    // ALWAYS USE API TIME AS PRIMARY SOURCE
                    // localStorage backup is only used to restore data that was lost during page unload
                    console.log(`API is authoritative source. API time: ${currentApiTime}s, Backup time: ${backupTimeInSeconds}s`);

                    // Only restore backup if it represents more recent usage (smaller time = more time consumed)
                    if (backupTimeInSeconds < currentApiTime) {
                      console.log(`Backup time (${backupTimeInSeconds}s) is less than API time (${currentApiTime}s), indicating more recent usage. Restoring backup.`);

                      try {
                        // Restore the backup to backend (this updates the database)
                        const updateResponse = await fetch(`${BASE_URL}${API_ENDPOINTS.USERS.ME}`, {
                          method: 'PUT',
                          headers: {
                            'Authorization': `Bearer ${data.access_token}`,
                            'Content-Type': 'application/json'
                          },
                          body: JSON.stringify({ remaining_time: backupTimeInSeconds })
                        });

                        if (updateResponse.ok) {
                          console.log(`Successfully restored backup remaining time for user ${userId}: ${backupTimeInSeconds}s`);
                          // Clear the backup since it's now in the database
                          localStorage.removeItem(userBackupKey);
                          console.log(`Cleared backup ${userBackupKey} after successful restoration`);

                          // Set flag to refresh user profile so PPCAchat gets updated API data
                          localStorage.setItem('user_profile_needs_refresh', 'true');
                        } else {
                          console.error(`Failed to restore backup remaining time for user ${userId}:`, updateResponse.status);
                          // Keep the backup for next login attempt
                          console.log(`Keeping backup ${userBackupKey} due to failed API update`);
                        }
                      } catch (updateError) {
                        console.error(`Error restoring backup remaining time for user ${userId}:`, updateError);
                        // Keep the backup for retry on next login
                        console.log(`Keeping backup ${userBackupKey} due to error during restoration`);
                      }
                    } else {
                      // API time is equal or more recent, backup is outdated
                      console.log(`API time (${currentApiTime}s) is equal or more recent than backup (${backupTimeInSeconds}s). Clearing outdated backup.`);
                      localStorage.removeItem(userBackupKey);
                    }
                  } else {
                    // Invalid backup data, clear it
                    console.log(`Invalid backup data for user ${userId}, clearing backup`);
                    localStorage.removeItem(userBackupKey);
                  }
                } else {
                  console.log(`No backup found for user ${userId}, using API time: ${currentApiTime} seconds`);
                }

                // Also clean up any old non-user-specific backup to prevent future contamination
                const oldBackup = localStorage.getItem('backup_remaining_time');
                if (oldBackup) {
                  console.log('Cleaning up old non-user-specific backup to prevent cross-user contamination');
                  localStorage.removeItem('backup_remaining_time');
                }
              }
            }
          } catch (profileError) {
            console.error('Error getting user profile for backup restoration:', profileError);

            // If we can't get user profile, clean up any old backup to be safe
            const oldBackup = localStorage.getItem('backup_remaining_time');
            if (oldBackup) {
              console.log('Cleaning up old backup due to profile error to prevent cross-user contamination');
              localStorage.removeItem('backup_remaining_time');
            }
          }
        } catch (profileError) {
          console.error('Error getting user profile during login:', profileError);
        } finally {
          // Always clear the timeout to prevent memory leaks
          clearTimeout(profileTimeoutId);
        }
      }

      return data;

    } catch (fetchError) {
      // Handle fetch errors
      // Only log detailed errors if it's not an empty object or invalid credentials
      if (fetchError && typeof fetchError === 'object') {
        if (Object.keys(fetchError).length > 0) {
          // Check if it's an invalid credentials error (401)
          const isInvalidCredentials =
            ('status' in fetchError && (fetchError as any).status === 401) ||
            ('status_code' in fetchError && (fetchError as any).status_code === 401) ||
            ('error_type' in fetchError && (fetchError as any).error_type === 'INVALID_CREDENTIALS');

          if (!isInvalidCredentials) {
            console.log('Fetch error during login:', fetchError);
          }
        } else {
          // Empty object error - just log a simple message
          console.log('Login failed with empty error object');
        }
      } else if (fetchError instanceof Error && !fetchError.message.includes('Invalid email or password')) {
        console.log('Fetch error during login:', fetchError.message);
      }

      // Re-throw the error
      throw fetchError;
    } finally {
      // Always clear the timeout to prevent memory leaks
      clearTimeout(timeoutId);
    }
  } catch (error) {
    // Only log non-empty objects and non-invalid-credentials errors
    if (error && typeof error === 'object') {
      if (Object.keys(error).length > 0) {
        // Check if it's an invalid credentials error (401)
        const isInvalidCredentials =
          ('status' in error && (error as any).status === 401) ||
          ('status_code' in error && (error as any).status_code === 401) ||
          ('error_type' in error && (error as any).error_type === 'INVALID_CREDENTIALS');

        if (!isInvalidCredentials) {
          // Non-credential error occurred
        }
      }
    }

    throw error;
  }
}

// Function to check if an email is already registered
export async function checkEmailExists(email: string): Promise<boolean> {
  try {
    console.log('Checking if email already exists:', email);

    // Since there's no dedicated check-email endpoint, we'll make a test signup request
    // with minimal/dummy data to check if the email exists. The backend will return 400 if email exists.
    // We use obviously fake data so if somehow a user gets created, it's clearly a test user.
    const testSignupData = {
      email: email,
      password: "TEST_PASSWORD_FOR_EMAIL_CHECK_123456", // Obviously fake password
      details: {
        first_name: "TEST_EMAIL_CHECK",
        last_name: "TEST_USER",
        country_code: "US",
        mobile_number: "+1",
        avatar_gender: "female",
        birthdate: null,
      }
    };

    const response = await fetch(`${BASE_URL}${API_ENDPOINTS.AUTH.SIGNUP}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testSignupData)
    });

    // If we get a 400 status code, check if it's because email already exists
    if (response.status === 400) {
      try {
        const data = await response.json();
        if (data && data.detail && typeof data.detail === 'string' &&
            data.detail.toLowerCase().includes('email already registered')) {
          console.log('Email already exists check: Email is already registered');
          return true;
        }
      } catch (e) {
        // If we can't parse the response but got 400, assume email exists
        console.log('Email already exists check: Got 400 status, assuming email exists');
        return true;
      }
    }

    // If we get here, either:
    // 1. The email is available (but we don't want to actually create the test user)
    // 2. There was some other error
    // In either case, we'll return false to allow the real signup to proceed
    console.log('Email already exists check: Email appears to be available');

    // IMPORTANT: If a test user was accidentally created, we should ideally delete it
    // but since we don't have a delete endpoint, we'll just log this for monitoring
    if (response.status === 200 || response.status === 201) {
      console.warn('WARNING: Test user may have been created during email check. Email:', email);
    }

    return false;
  } catch (error) {
    console.error('Error checking if email exists:', error);
    // In case of error, return false to allow the signup attempt
    // The actual signup will handle any errors properly
    return false;
  }
}

// Track ongoing signup requests to prevent duplicates
const ongoingSignupRequests = new Map<string, Promise<any>>();

export async function signup(userData: SignupRequest): Promise<any> {
  try {
    // Check if there's already an ongoing request for this email
    const email = userData.email.toLowerCase();
    if (ongoingSignupRequests.has(email)) {
      console.log('Signup request already in progress for email:', email);
      return ongoingSignupRequests.get(email);
    }

    // Removed email existence check - let the backend handle duplicate email validation
    // This prevents the duplicate signup requests that were causing issues
    console.log('Proceeding with signup, backend will handle email validation');

    // Email validation will be handled by the backend, proceeding with signup

    // Create the signup promise and store it
    const signupPromise = performSignupRequest(userData);
    ongoingSignupRequests.set(email, signupPromise);

    try {
      const result = await signupPromise;
      return result;
    } finally {
      // Remove from ongoing requests when completed
      ongoingSignupRequests.delete(email);
    }
  } catch (error) {
    // Remove from ongoing requests on error
    const email = userData.email.toLowerCase();
    ongoingSignupRequests.delete(email);
    throw error;
  }
}

async function performSignupRequest(userData: SignupRequest): Promise<any> {
  // Add a timeout to the fetch request - longer timeout for better user experience
  const controller = new AbortController();
  const timeoutDuration = 30000; // 30 second timeout for better chance of success
  const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

  try {
    const response = await fetch(`${BASE_URL}${API_ENDPOINTS.AUTH.SIGNUP}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
        let errorMessage = 'Signup failed';
        let errorType = 'UNKNOWN_ERROR';

        try {
          // First, try to parse the response as JSON
          let errorData;

          try {
            errorData = await response.json();
            console.log('Response status:', response.status, 'Response data:', errorData);

            // Check if we have valid error data
            if (errorData && Object.keys(errorData).length > 0) {
              // Special handling for "Email already registered" error
              if (errorData.detail && typeof errorData.detail === 'string' &&
                  errorData.detail.toLowerCase().includes('email already registered')) {
                console.log('Email already registered error detected from backend');
                errorMessage = 'Email already registered. Please use a different email address or login with your existing account.';
                errorType = 'EMAIL_ALREADY_REGISTERED';
              } else {
                // For other errors, use the detail from the response
                errorMessage = errorData.detail || errorMessage;
              }
            } else {
              console.error('Backend returned empty error response with status:', response.status);
            }
          } catch (jsonError) {
            console.error('Failed to parse error response as JSON:', jsonError);
          }

          // If we couldn't get error details from JSON, use status code to determine error
          if (response.status === 400) {
            // For 400 status code without specific error details, assume it's an "Email already registered" error
            // This is based on the backend code you shared
            errorMessage = 'Email already registered. Please use a different email address or login with your existing account.';
            errorType = 'EMAIL_ALREADY_REGISTERED';
            console.log('Assuming 400 status code is for Email already registered');
          } else if (response.status === 422) {
            errorMessage = 'Validation error: The server could not validate the data.';
            errorType = 'VALIDATION_ERROR';
          } else if (response.status === 500) {
            errorMessage = 'The server encountered a database connection error. Please try again later.';
            errorType = 'SERVER_ERROR';
          }

        } catch (e) {
          console.error('Error handling response:', e);

          // If all else fails, use status code to determine error
          if (response.status === 400) {
            // Received 400 status code - email already registered
            errorMessage = 'Email already registered. Please use a different email address or login with your existing account.';
            errorType = 'EMAIL_ALREADY_REGISTERED';

            // EMAIL_ALREADY_REGISTERED error detected
          } else if (response.status === 500) {
            errorMessage = 'The server encountered a database connection error. Please try again later.';
            errorType = 'SERVER_ERROR';
          } else {
            errorMessage = `Signup failed: ${response.status} ${response.statusText}`;
          }
        }

        // Create an ApiError object with the error message and status code
        const apiError: ApiError = {
          detail: errorMessage,
          status_code: response.status,
          error_type: errorType
        };

        // For "Email already registered" errors, handle them specially
        if (errorType === 'EMAIL_ALREADY_REGISTERED') {

          // Return a special response object instead of throwing an error
          return {
            success: false,
            error: true,
            errorType: 'EMAIL_ALREADY_REGISTERED',
            message: errorMessage,
            status: response.status
          };
        }

        // API error occurred

        // Throw the error with the ApiError details for other error types
        throw new Error(`${apiError.error_type}: ${apiError.detail}`);
    }

    // Process the actual response from the server
    console.log('Signup API call successful, response status:', response.status);

    try {
      const responseData = await response.json();
      console.log('Signup API response data:', responseData);
      return {
        success: true,
        message: 'Signup successful',
        ...responseData
      };
    } catch (jsonError) {
      console.error('Error parsing JSON response:', jsonError);

      // If we can't parse the response but it was successful, return a generic success
      if (response.ok) {
        return {
          success: true,
          message: 'Signup successful (empty response)'
        };
      }

      throw new Error('Failed to parse server response');
    }
  } catch (fetchError) {
      console.error('Fetch error during signup:', fetchError);

      if (fetchError && typeof fetchError === 'object' && 'name' in fetchError && fetchError.name === 'AbortError') {
        // Signup request timed out - server might be unavailable

        // Throw a user-friendly error
        throw new Error('SERVER_ERROR: The server is taking too long to respond. Please try again later.');
      }

      // For network errors (like CORS or connection refused), provide more helpful messages
      if (fetchError instanceof TypeError && fetchError.message.includes('Failed to fetch')) {
        console.error('Network error - backend server may be unavailable or CORS issues');
        throw new Error('SERVER_ERROR: Unable to connect to the server. Please check your network connection and try again.');
      }

      throw fetchError;
  } finally {
    clearTimeout(timeoutId);
  }
}

export async function authenticatedRequest(endpoint: string, options: RequestInit = {}): Promise<Response> {
  console.time(`auth_request_${endpoint}`); // Start timing for authenticated request

  const token = localStorage.getItem('token');
  const isConfirmationPage = typeof window !== 'undefined' && window.location.pathname.includes('/confirmation');
  const isPPCAPage = typeof window !== 'undefined' && window.location.pathname.includes('/ppca');

  if (!token) {
    console.log('No token found in authenticatedRequest');

    // On PPCA page, be more graceful about missing tokens
    if (isPPCAPage) {
      console.log('On PPCA page, handling missing token gracefully');

      // Check if we're trying to update remaining time
      if (endpoint === API_ENDPOINTS.USERS.ME && options.method === 'PUT') {
        console.log('Attempting to update user data without token on PPCA page, returning graceful error');
        console.timeEnd(`auth_request_${endpoint}`); // End timing before early return
        // Return a fake response that can be handled gracefully
        return Promise.reject({
          status: 401,
          json: () => Promise.resolve({ success: false, error: 'No token' })
        });
      }
    }

    // On confirmation page, try to recover by creating a new token
    if (isConfirmationPage) {

      // Check if there's an "Email already registered" error in localStorage
      const tempError = localStorage.getItem('temp_auth_error');
      if (tempError && tempError.includes('Email already registered')) {
        // Email already registered error detected
        console.timeEnd(`auth_request_${endpoint}`); // End timing before early return
        return Promise.reject(new Response(null, { status: 401 }));
      }

      // Try to get email and password from signupService
      try {
        const signupService = (window as any).signupService;
        if (signupService) {
          const email = signupService.getEmail();
          const password = signupService.getPassword();

          if (email && password) {
            console.log('Found email and password, attempting to login');
            try {
              // Try to login to get a new token
              await login(email, password);
              console.log('Successfully recovered token');

              // Retry the request with the new token
              return authenticatedRequest(endpoint, options);
            } catch (loginError) {
              console.error('Failed to recover token:', loginError);

              // Check if it's an "Email already registered" error
              if (loginError && typeof loginError === 'object' &&
                  ('detail' in loginError) &&
                  typeof (loginError as any).detail === 'string' &&
                  (loginError as any).detail.includes('Email already registered')) {
                console.log('Email already registered error detected during login, not retrying');
                localStorage.setItem('temp_auth_error', 'Email already registered');
                console.timeEnd(`auth_request_${endpoint}`); // End timing before early return
                return Promise.reject(new Response(null, { status: 401 }));
              }
            }
          }
        }
      } catch (recoveryError) {
        console.error('Error during token recovery attempt:', recoveryError);
      }

      // If recovery failed, continue without setting error message
      console.log('Token recovery failed, continuing without error message');
    } else {
      // Set a temporary error message for other pages
      localStorage.setItem('temp_auth_error', 'Your session has expired. Please log in again.');
    }

    // Return a rejected promise to stop further execution
    console.timeEnd(`auth_request_${endpoint}`); // End timing before early return
    return Promise.reject(new Response(null, { status: 401 }));
  }

  const headers = {
    ...options.headers,
    'Authorization': `Bearer ${token}`
  };

  try {

    // Create a new AbortController if one wasn't provided
    let abortController: AbortController | null = null;
    const signal = options.signal || (abortController = new AbortController()).signal;

    // If no timeout was set and no signal was provided, add a default timeout
    if (!options.signal && abortController) {
      const timeoutDuration = 15000; // 15 second timeout
      const timeoutId = setTimeout(() => {
        if (abortController) {
          abortController.abort();
        }
      }, timeoutDuration);

      // Clean up the timeout when the request completes
      setTimeout(() => clearTimeout(timeoutId), timeoutDuration + 100);
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, {
      ...options,
      headers,
      signal
    });

    if (response.status === 401) {
      // Token expired or invalid
      console.log('Token is invalid or expired (401 response)');

      // On confirmation page, don't clear token and try to recover
      if (isConfirmationPage) {
        console.log('On confirmation page, not clearing token despite 401 response');

        // Check if there's an "Email already registered" error in localStorage
        const tempError = localStorage.getItem('temp_auth_error');
        if (tempError && tempError.includes('Email already registered')) {
          console.log('Email already registered error detected, not attempting token refresh');
          console.timeEnd(`auth_request_${endpoint}`); // End timing before early return
          return Promise.reject(new Response(null, { status: 401 }));
        }

        // Try to get email and password from signupService
        try {
          const signupService = (window as any).signupService;
          if (signupService) {
            const email = signupService.getEmail();
            const password = signupService.getPassword();

            if (email && password) {
              console.log('Found email and password, attempting to login');
              try {
                // Try to login to get a new token
                await login(email, password);
                console.log('Successfully refreshed token');

                // Retry the request with the new token
                return authenticatedRequest(endpoint, options);
              } catch (loginError) {
                console.error('Failed to refresh token:', loginError);

                // Check if it's an "Email already registered" error
                if (loginError && typeof loginError === 'object' &&
                    ('detail' in loginError) &&
                    typeof (loginError as any).detail === 'string' &&
                    (loginError as any).detail.includes('Email already registered')) {
                  console.log('Email already registered error detected during login, not retrying');
                  localStorage.setItem('temp_auth_error', 'Email already registered');
                  console.timeEnd(`auth_request_${endpoint}`); // End timing before early return
                  return Promise.reject(new Response(null, { status: 401 }));
                }
              }
            }
          }
        } catch (recoveryError) {
          console.error('Error during token refresh attempt:', recoveryError);
        }
      } else {
        // Clear token for other pages
        localStorage.removeItem('token');

        // Set a temporary error message
        localStorage.setItem('temp_auth_error', 'Your session has expired. Please log in again.');
      }

      // Return a rejected promise to stop further execution
      console.timeEnd(`auth_request_${endpoint}`); // End timing before early return
      return Promise.reject(new Response(null, { status: 401 }));
    }

    if (!response.ok) {
      try {
        const errorData = await response.json();
        // Create a structured error object with status code and details
        const apiError = {
          status: response.status,
          status_code: response.status,
          detail: errorData.detail || 'Request failed',
          error_type: response.status === 401 ? 'AUTHENTICATION_ERROR' :
                     response.status === 400 ? 'INVALID_REQUEST' :
                     response.status === 403 ? 'FORBIDDEN' :
                     response.status === 404 ? 'NOT_FOUND' :
                     response.status === 500 ? 'SERVER_ERROR' : 'UNKNOWN_ERROR'
        };
        throw apiError;
      } catch (jsonError) {
        // If jsonError is already a structured error (from the try block), re-throw it
        if (jsonError && typeof jsonError === 'object' && ('status' in jsonError || 'status_code' in jsonError)) {
          throw jsonError;
        }

        // If we can't parse the response as JSON, create a structured error
        const apiError = {
          status: response.status,
          status_code: response.status,
          detail: `Request failed with status: ${response.status}`,
          error_type: response.status === 401 ? 'AUTHENTICATION_ERROR' :
                     response.status === 400 ? 'INVALID_REQUEST' :
                     response.status === 403 ? 'FORBIDDEN' :
                     response.status === 404 ? 'NOT_FOUND' :
                     response.status === 500 ? 'SERVER_ERROR' : 'UNKNOWN_ERROR'
        };
        throw apiError;
      }
    }

    console.timeEnd(`auth_request_${endpoint}`); // End timing for authenticated request
    return response;
  } catch (error) {
    console.timeEnd(`auth_request_${endpoint}`); // End timing for authenticated request even on error

    // Handle network errors (like CORS, connection refused)
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      console.error('Network error in authenticatedRequest:', error);

      // Check if we're on the confirmation page
      if (isConfirmationPage) {
        console.log('On confirmation page, not setting error message despite network error');
      } else {
        // Set a temporary error message about connection issues for other pages
        localStorage.setItem('temp_auth_error', 'Unable to connect to the server. Please check your network connection.');
      }
    }

    throw error;
  }
}

export async function updateUserDetails(details: Partial<UserDetails>): Promise<UserProfile> {
  try {
    console.log('Updating user details:', details);

    // Make the API call to update user details
    const response = await authenticatedRequest(API_ENDPOINTS.USERS.ME, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ details })
    });

    if (!response.ok) {
      throw new Error(`Failed to update user details: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating user details:', error);
    throw error;
  }
}

/**
 * Update the user's language preference
 * @param language - The language code (e.g., 'en', 'de')
 * @returns The updated user profile
 */
export async function updateUserLanguage(language: string): Promise<UserProfile> {
  try {
    console.log('Updating user language preference to:', language);

    // Use the updateUserDetails function to update just the language field
    return await updateUserDetails({
      language: language
    });
  } catch (error) {
    console.error('Error updating user language preference:', error);
    throw error;
  }
}

/**
 * Utility function to get user-specific backup key for remaining time
 * @param userId - Optional user ID, if not provided will try to get from current user
 * @returns Promise<string> - The user-specific backup key
 */
async function getUserBackupKey(userId?: string): Promise<string> {
  if (userId) {
    return `backup_remaining_time_user_${userId}`;
  }

  // Try to get user ID from current user profile
  try {
    const token = localStorage.getItem('token');
    if (token) {
      const profileResponse = await fetch(`${BASE_URL}${API_ENDPOINTS.USERS.ME}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (profileResponse.ok) {
        const userProfile = await profileResponse.json();
        const currentUserId = userProfile.id || userProfile.user_id;
        if (currentUserId) {
          return `backup_remaining_time_user_${currentUserId}`;
        }
      }
    }
  } catch (error) {
    console.log('Could not get user ID for backup key, using fallback');
  }

  // Fallback to old key format
  return 'backup_remaining_time';
}

/**
 * Utility function to clean up old non-user-specific backup
 */
function cleanupOldBackup(): void {
  const oldBackup = localStorage.getItem('backup_remaining_time');
  if (oldBackup) {
    console.log('Cleaning up old non-user-specific backup to prevent cross-user contamination');
    localStorage.removeItem('backup_remaining_time');
  }
}

export async function updateRemainingTime(remainingTimeInSeconds: number, userId?: string): Promise<any> {
  try {
    console.log(`Updating user's remaining time to ${remainingTimeInSeconds} seconds (${Math.floor(remainingTimeInSeconds / 60)} minutes and ${remainingTimeInSeconds % 60} seconds)`);

    // Get user-specific backup key
    const userBackupKey = await getUserBackupKey(userId);
    console.log(`Using backup key: ${userBackupKey}`);

    // Store the remaining time in seconds in localStorage as a user-specific backup
    // This will be used if the API call fails due to token expiration
    localStorage.setItem(userBackupKey, remainingTimeInSeconds.toString());
    console.log(`Stored user-specific backup remaining time in localStorage: ${remainingTimeInSeconds} seconds with key: ${userBackupKey}`);

    // Clean up any old non-user-specific backup to prevent contamination
    if (userBackupKey !== 'backup_remaining_time') {
      cleanupOldBackup();
    }

    // Check if token exists before trying to make the API call
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in updateRemainingTime, using backup only');
      return { success: true, backup_used: true };
    }

    // Make the API call to update remaining time
    const response = await authenticatedRequest(API_ENDPOINTS.USERS.ME, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ remaining_time: remainingTimeInSeconds })
    });

    // Clear the user-specific backup if the API call succeeds
    localStorage.removeItem(userBackupKey);
    console.log(`API call successful, removed user-specific backup remaining time from localStorage: ${userBackupKey}`);

    return await response.json();
  } catch (error) {
    console.error('Error updating remaining time:', error);

    // Check if it's an authentication error (401)
    if (error instanceof Response && error.status === 401) {
      console.log('Authentication error (401) during updateRemainingTime - token may have expired');
      // Don't throw, just return success since we've stored the backup
      return { success: true, backup_used: true };
    }

    // For other errors, still propagate but don't prevent logout
    return { success: false, error: 'Failed to update remaining time' };
  }
}

export async function setUserStage(stage: number): Promise<void> {
  try {
    // Make the API call to update the user's stage
    const response = await authenticatedRequest(`${API_ENDPOINTS.JOURNEY.SET_STAGE}/${stage}`, {
      method: 'POST'
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Failed to set user stage: ${response.status} ${JSON.stringify(errorData)}`);
    }

    // Ensure we return successfully when the request completes
    return;
  } catch (error) {
    console.error('Error setting user stage:', error);
    throw error;
  }
}

export interface AssistantStatusResponse {
  status: 'not_started' | 'in_progress' | 'complete';
}

export async function checkAssistantStatus(): Promise<AssistantStatusResponse> {
  try {
    const response = await authenticatedRequest(API_ENDPOINTS.ASSISTANTS.STATUS, {
      method: 'GET'
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Failed to check assistant status: ${response.status} ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error checking assistant status:', error);
    throw error;
  }
}

export async function getUserProfile(): Promise<UserProfile> {
  try {
    // Check if there's an "Email already registered" error in localStorage
    const tempError = localStorage.getItem('temp_auth_error');
    if (tempError && tempError.includes('Email already registered')) {
      console.log('Email already registered error detected in getUserProfile, not attempting API call');
      throw new Error('Email already registered');
    }

    // Check if we have a token first
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No token found in getUserProfile');

      // Check if we're on the confirmation page
      const isConfirmationPage = typeof window !== 'undefined' &&
                                window.location.pathname.includes('/confirmation');

      if (isConfirmationPage) {
        console.log('On confirmation page with no token, attempting to login');

        // Try to get email and password from signupService
        try {
          const signupService = (window as any).signupService;
          if (signupService) {
            const email = signupService.getEmail();
            const password = signupService.getPassword();

            if (email && password) {
              console.log('Found email and password, attempting to login');
              try {
                // Try to login to get a new token
                await login(email, password);
                console.log('Successfully created token in getUserProfile');

                // Now that we have a token, retry the request
                return getUserProfile();
              } catch (loginError) {
                console.log('Failed to create token in getUserProfile:', loginError);
                throw new Error('Failed to retrieve token');
              }
            } else {
              console.log('No email/password available for login');
              throw new Error('No credentials available to retrieve token');
            }
          } else {
            console.log('SignupService not available');
            throw new Error('No credentials available to retrieve token');
          }
        } catch (recoveryError) {
          console.error('Error during token creation attempt:', recoveryError);
          throw new Error('Failed to retrieve token');
        }
      } else {
        // Not on confirmation page, just throw an error
        throw new Error('No token available');
      }
    }

    // Always try to make the real API call first
    console.log('Attempting to fetch user profile from API');
    console.time('get_user_profile_api_call'); // Add timing for API call

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutDuration = 10000; // 10 second timeout
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

    try {
      // Normal API call for production with timeout
      const response = await authenticatedRequest(API_ENDPOINTS.USERS.ME, {
        signal: controller.signal
      });
      console.timeEnd('get_user_profile_api_call'); // End timing for API call

      console.time('get_user_profile_parse'); // Add timing for parsing
      const result = await response.json();
      console.timeEnd('get_user_profile_parse'); // End timing for parsing

      return result;
    } finally {
      clearTimeout(timeoutId); // Always clear the timeout
    }
  } catch (error) {
    // Don't log the full error object for 401 errors or "Email already registered" errors
    if (error instanceof Response && error.status === 401) {
      console.log('Authentication error in getUserProfile - propagating');

      // Check if we're on the confirmation page
      const isConfirmationPage = typeof window !== 'undefined' &&
                                window.location.pathname.includes('/confirmation');

      if (isConfirmationPage) {
        console.log('On confirmation page with 401 error, attempting to login');

        // Try to get email and password from signupService
        try {
          const signupService = (window as any).signupService;
          if (signupService) {
            const email = signupService.getEmail();
            const password = signupService.getPassword();

            if (email && password) {
              console.log('Found email and password, attempting to login');
              try {
                // Try to login to get a new token
                await login(email, password);
                console.log('Successfully refreshed token in getUserProfile');

                // Now that we have a token, retry the request
                return getUserProfile();
              } catch (loginError) {
                console.log('Failed to refresh token in getUserProfile:', loginError);
              }
            }
          }
        } catch (recoveryError) {
          console.error('Error during token refresh attempt:', recoveryError);
        }
      }
    } else if (error instanceof Error && error.message.includes('Email already registered')) {
      console.log('Email already registered error in getUserProfile - propagating');
    } else if (error instanceof Error && error.message.includes('Failed to retrieve token')) {
      console.log('Token retrieval error in getUserProfile - propagating');
    } else {
      console.log('Error getting user profile:', error);
    }

    // If it's a Response object with status 401, just propagate it
    // The authenticatedRequest function will handle the redirection
    if (error instanceof Response && error.status === 401) {
      throw error;
    }

    // For "Email already registered" errors, throw a special error
    if (error instanceof Error && error.message.includes('Email already registered')) {
      throw new Error('Email already registered');
    }

    // Throw the error
    throw error instanceof Error ? error : new Error('Failed to fetch user profile');
  }
}

export async function requestPasswordReset(email: string): Promise<boolean> {
  try {
    console.log('Requesting password reset for email:', email);

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutDuration = 15000; // 15 second timeout
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

    const response = await fetch(`${BASE_URL}${API_ENDPOINTS.AUTH.FORGOT_PASSWORD}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    // The backend should return 200 OK regardless of whether the email exists
    // This is a security measure to prevent email enumeration
    if (response.ok) {
      console.log('Password reset request successful');
      return true;
    } else {
      const errorData = await response.json();
      console.error('Password reset request failed:', errorData);
      throw new Error(errorData.detail || 'Failed to request password reset');
    }
  } catch (error) {
    console.error('Error requesting password reset:', error);
    throw error;
  }
}

export async function resetPassword(token: string, newPassword: string): Promise<boolean> {
  try {
    console.log('Resetting password with token');

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutDuration = 15000; // 15 second timeout
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

    const response = await fetch(`${BASE_URL}${API_ENDPOINTS.AUTH.RESET_PASSWORD}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: token,
        new_password: newPassword
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      console.log('Password reset successful');
      return true;
    } else {
      const errorData = await response.json();
      console.error('Password reset failed:', errorData);
      throw new Error(errorData.detail || 'Failed to reset password');
    }
  } catch (error) {
    console.error('Error resetting password:', error);
    throw error;
  }
}

export async function logout(): Promise<void> {
  // Variables to store the user-specific backup remaining time
  let userBackupKey: string | null = null;
  let backupRemainingTime: string | null = null;

  try {
    // Get user ID to preserve the correct user's backup
    try {
      const token = localStorage.getItem('token');
      if (token) {
        const profileResponse = await fetch(`${BASE_URL}${API_ENDPOINTS.USERS.ME}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (profileResponse.ok) {
          const userProfile = await profileResponse.json();
          const userId = userProfile.id || userProfile.user_id;
          if (userId) {
            userBackupKey = `backup_remaining_time_user_${userId}`;
            backupRemainingTime = localStorage.getItem(userBackupKey);
            console.log(`Found user-specific backup for user ${userId}: ${backupRemainingTime} seconds`);
          }
        }
      }
    } catch (profileError) {
      console.log('Could not get user profile for backup preservation during logout');
    }

    // Fallback: check for old non-user-specific backup
    if (!backupRemainingTime) {
      backupRemainingTime = localStorage.getItem('backup_remaining_time');
      if (backupRemainingTime) {
        console.log('Found old non-user-specific backup, will preserve it');
      }
    }

    // Clean up audio and WebSocket connections before logout
    console.log('Cleaning up audio and WebSocket connections before logout');
    try {
      // Import audio utilities dynamically to avoid SSR issues
      if (typeof window !== 'undefined') {
        const audioUtils = await import('../utils/audioStreamingStatic');

        // Set unmounting flag to prevent new operations
        audioUtils.setUnmountingFlag(true);

        // Clear audio queue and stop any ongoing audio
        audioUtils.clearAudioQueue();

        // Close all audio and WebSocket connections
        audioUtils.closeConnection();

        console.log('Audio and WebSocket cleanup completed');
      }
    } catch (cleanupError) {
      console.log('Error during audio cleanup (continuing with logout):', cleanupError);
    }

    // Always try to make the real API call first
    console.log('Attempting to logout via API');
    try {
      await authenticatedRequest(API_ENDPOINTS.AUTH.LOGOUT, {
        method: 'POST'
      });
      console.log('Logout API call successful');
    } catch (error) {
      // Check if it's an empty object error
      if (error && typeof error === 'object' && Object.keys(error).length === 0) {
        console.log('Empty object error during logout - this is expected and can be ignored');
      } else if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.log('Network error during logout - server may be unreachable, continuing with local logout');
      } else {
        console.error('Error during logout API call:', error);
      }

      // Always suppress errors in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Development mode - continuing with local logout despite API error');
      } else {
        // In production, only throw if it's not an empty object error
        if (!(error && typeof error === 'object' && Object.keys(error).length === 0)) {
          throw error;
        }
      }
    }
  } finally {
    // If we didn't get the backup remaining time earlier, try to get it now
    if (backupRemainingTime === null) {
      // Try user-specific backup first
      if (userBackupKey) {
        backupRemainingTime = localStorage.getItem(userBackupKey);
      }
      // Fallback to old backup
      if (!backupRemainingTime) {
        backupRemainingTime = localStorage.getItem('backup_remaining_time');
      }
    }

    // Always remove ALL items from localStorage
    console.log('Clearing all localStorage items during logout');
    localStorage.clear();

    // Restore the user-specific backup remaining time if it exists
    if (backupRemainingTime && userBackupKey) {
      console.log(`Preserving user-specific backup remaining time during logout: ${backupRemainingTime} seconds (${Math.floor(parseInt(backupRemainingTime, 10) / 60)} minutes and ${parseInt(backupRemainingTime, 10) % 60} seconds) with key: ${userBackupKey}`);
      localStorage.setItem(userBackupKey, backupRemainingTime);
    } else if (backupRemainingTime) {
      // Fallback: preserve old backup format (will be cleaned up on next login)
      console.log(`Preserving old backup remaining time during logout: ${backupRemainingTime} seconds (${Math.floor(parseInt(backupRemainingTime, 10) / 60)} minutes and ${parseInt(backupRemainingTime, 10) % 60} seconds)`);
      localStorage.setItem('backup_remaining_time', backupRemainingTime);
    }

    // We'll handle redirection in the component
  }
}





export async function changePassword(currentPassword: string, newPassword: string): Promise<boolean> {
  try {
    console.log('Changing password');

    // Add a timeout to the fetch request
    const controller = new AbortController();
    const timeoutDuration = 15000; // 15 second timeout
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

    const response = await authenticatedRequest(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        current_password: currentPassword,
        new_password: newPassword
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    console.log('Password change successful');
    return true;
  } catch (error) {
    console.error('Error changing password:', error);

    // If it's already a structured error from authenticatedRequest, re-throw it
    if (error && typeof error === 'object' && ('status' in error || 'status_code' in error)) {
      throw error;
    }

    // Handle network errors and other exceptions
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw {
          status: 408,
          status_code: 408,
          detail: 'Request timeout. Please try again.',
          error_type: 'TIMEOUT_ERROR'
        };
      } else if (error.message.includes('Failed to fetch') || error.message.includes('Network')) {
        throw {
          status: 0,
          status_code: 0,
          detail: 'Network error. Please check your internet connection.',
          error_type: 'NETWORK_ERROR'
        };
      }
    }

    // For any other errors, wrap them in a structured format
    throw {
      status: 0,
      status_code: 0,
      detail: error instanceof Error ? error.message : 'An unexpected error occurred',
      error_type: 'UNKNOWN_ERROR'
    };
  }
}

export async function handleApiError(error: unknown): Promise<ApiError> {
  // First, check if the error is an object with status, detail, and error_type properties
  if (error && typeof error === 'object') {
    // Check if it's already an ApiError-like object
    if ('detail' in error || 'status_code' in error || 'error_type' in error || 'status' in error) {
      const apiError = error as any;

      // Handle 401 status codes specifically for invalid credentials
      if (apiError.status === 401 || apiError.status_code === 401) {
        return {
          detail: apiError.detail || 'Invalid email or password',
          error_type: 'INVALID_CREDENTIALS',
          status_code: 401
        };
      }

      return {
        detail: apiError.detail || 'An error occurred',
        error_type: apiError.error_type || 'UNKNOWN_ERROR',
        status_code: apiError.status || apiError.status_code || 0
      };
    }

    // Check if it's an empty object
    if (Object.keys(error).length === 0) {
      return {
        detail: 'An unexpected error occurred',
        error_type: 'UNKNOWN_ERROR',
        status_code: 0
      };
    }
  }

  if (error instanceof Error) {
    // Check if the error message contains an error type prefix
    const errorTypeMatch = error.message.match(/^([A-Z_]+):\s(.+)$/);
    if (errorTypeMatch) {
      const [, errorType, errorDetail] = errorTypeMatch;
      return {
        detail: errorDetail,
        error_type: errorType,
        status_code: errorType === 'EMAIL_ALREADY_REGISTERED' ? 400 :
                    errorType === 'VALIDATION_ERROR' ? 422 :
                    errorType === 'AUTHENTICATION_FAILED' ? 401 :
                    errorType === 'ACCESS_FORBIDDEN' ? 403 :
                    errorType === 'NOT_FOUND' ? 404 :
                    errorType === 'SERVER_ERROR' ? 500 : 0
      };
    }

    // If no error type prefix, return a generic error
    return {
      detail: error.message,
      error_type: 'UNKNOWN_ERROR',
      status_code: 0
    };
  }

  // For non-Error objects
  return {
    detail: 'An unexpected error occurred',
    error_type: 'UNKNOWN_ERROR',
    status_code: 0
  };
}
