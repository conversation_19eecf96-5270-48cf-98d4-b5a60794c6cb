"use client";

import { useState } from "react";
import { getTranslation } from "../i18n/translations";
import { setUserStage } from "../services/api";

export default function WelcomePage({
  lng,
  onComplete
}: {
  lng: string,
  onComplete: () => void
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Use the t function from our static translations
  const t = (key: string) => getTranslation(key, lng);

  // Handle start button click
  const handleStart = async () => {
    try {
      setIsLoading(true);
      setError("");

      // Update the user's stage to 2 (AVATAR_SELECTION)
      await setUserStage(2);

      // Call the onComplete callback to move to the avatar selection screen
      onComplete();
    } catch (error) {
      console.error('Failed to update stage:', error);
      setError(t("error_start_failed") || "Failed to start. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="text-center max-w-xs sm:max-w-sm md:max-w-md mx-auto px-2 sm:px-0">
      {error && (
        <div className="p-2 sm:p-3 mb-3 sm:mb-4 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 text-red-500 rounded-md text-xs sm:text-sm">
          {error}
        </div>
      )}

      <div className="flex flex-col justify-center items-center">
        {/* Welcome image */}
        <div className="flex justify-center items-center mb-4 sm:mb-6 relative w-full">
          <div className="relative mx-auto">
            <div className="w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 overflow-hidden">
              <img
                src="/images/logo_icon.png"
                alt="Welcome"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>

        {/* Welcome text */}
        <div className="mb-4 sm:mb-6 md:mb-8 text-lg sm:text-xl font-semibold text-gray-800 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] bg-clip-text text-transparent">
          {t("welcome_title") || "Welcome to Luma Life!"}
        </div>

        <div className="mb-6 sm:mb-8 text-sm sm:text-base text-gray-600">
          {lng === "de" ? (
            <p>
              Willkommen bei Luma Life, deiner persönlichen Assistenz-App für mehr Selbstständigkeit im Leben mit Demenz. Klicke auf{" "}
              <span className="inline-block font-extrabold text-[#52bcc3] px-1 py-0.5 rounded-md">Start</span>, um zu beginnen.
            </p>
          ) : (
            <p>
              Luma is your personal AI everyday assistant. Click{" "}
              <span className="inline-block font-extrabold text-[#52bcc3] px-1 py-0.5 rounded-md">Start</span> to begin your journey.
            </p>
          )}
        </div>
      </div>

      <div className="mt-4 sm:mt-6">
        <button
          onClick={handleStart}
          disabled={isLoading}
          className={`min-w-[100px] sm:min-w-[120px] py-2 sm:py-2.5 px-4 sm:px-6 ${
            isLoading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer"
          } text-white text-sm sm:text-base rounded-full font-extrabold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap`}
        >
          {isLoading ? (t("starting") || "Starting...") : (t("start") || "Start")}
        </button>
      </div>
    </div>
  );
}
