// Admin API Service for handling admin-specific backend requests
import { UserDetails, API_ENDPOINTS } from './api';
import { API_BASE_URL } from '../config/appConfig';
const BASE_URL = API_BASE_URL;

// Enums
export enum UserStageEnum {
  SIGNUP = 'signup',
  DETAILS = 'details',
  ORGANIZATION = 'organization',
  AVATAR = 'avatar',
  COMPLETE = 'complete'
}

export enum RoleEnum {
  USER = 'user',
  ADMIN = 'admin'
}

// Types
export interface UserDetailsOut {
  first_name: string;
  last_name: string;
  country_code: string;
  mobile_number: string;
  avatar_gender: string;
  birthdate: string;
  org_name?: string;
  language?: string; // User's preferred language (e.g., 'en', 'de')
}

export interface UserList {
  id: number;
  email: string;
  is_approved: boolean;
  current_stage: UserStageEnum;
  role: RoleEnum;
  max_token_limit: number;
  tokens_used: number;
  tokens_remaining: number;
  remaining_time: number;
  created_at: string;
  details: UserDetailsOut;
}

export interface UserListResponse {
  items: UserList[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface UserListParams {
  page?: number;
  limit?: number;
  is_approved?: boolean;
  role?: string;
  // Removed gender parameter as requested
  current_stage?: string;
  email?: string;
  sort_by?: string;
  sort_desc?: boolean;
  created_after?: string;
  created_before?: string;
}

/**
 * Helper function to make authenticated requests to the admin API
 */
async function authenticatedAdminRequest(endpoint: string, options: RequestInit = {}) {
  const token = localStorage.getItem('token');

  if (!token) {
    throw new Error('No authentication token');
  }

  // Ensure headers exist
  if (!options.headers) {
    options.headers = {};
  }

  // Add authorization header
  options.headers = {
    ...options.headers,
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  // Make the request
  const response = await fetch(`${BASE_URL}${endpoint}`, options);

  // Handle unauthorized responses
  if (response.status === 401) {
    // Clear token and throw error
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userEmail');
    throw new Error('Unauthorized: Please login again');
  }

  return response;
}

/**
 * Bulk approve or unapprove users
 */
export async function bulkApproveUsers(userIds: number[], approve: boolean): Promise<any> {
  try {
    console.log(`${approve ? 'Approving' : 'Unapproving'} users with IDs:`, userIds);

    // Build query string
    const queryParams = new URLSearchParams();
    queryParams.append('approve', approve.toString());

    const endpoint = `${API_ENDPOINTS.ADMIN.USERS.BULK_APPROVE}?${queryParams.toString()}`;

    // Make the API call
    const response = await authenticatedAdminRequest(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userIds),
    });

    if (!response.ok) {
      throw new Error(`Failed to ${approve ? 'approve' : 'unapprove'} users: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error ${approve ? 'approving' : 'unapproving'} users:`, error);
    throw error;
  }
}

/**
 * Get a list of users with optional filtering and pagination
 */
export async function listUsers(params: UserListParams = {}): Promise<UserListResponse> {
  try {
    console.log('Fetching user list with params:', params);

    // Build query string from params
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.is_approved !== undefined) queryParams.append('is_approved', params.is_approved.toString());
    if (params.role) queryParams.append('role', params.role);
    // Removed gender/avatar_gender filter as requested
    if (params.current_stage) queryParams.append('current_stage', params.current_stage);
    if (params.email) queryParams.append('email', params.email);
    // Add sorting parameters
    if (params.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params.sort_desc !== undefined) queryParams.append('sort_desc', params.sort_desc.toString());
    // Add date filtering parameters
    if (params.created_after) queryParams.append('created_after', params.created_after);
    if (params.created_before) queryParams.append('created_before', params.created_before);

    const queryString = queryParams.toString();
    const endpoint = `${API_ENDPOINTS.ADMIN.USERS.LIST}${queryString ? `?${queryString}` : ''}`;

    // Make the API call
    const response = await authenticatedAdminRequest(endpoint);

    if (!response.ok) {
      throw new Error(`Failed to fetch users: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Check if the response is an array (direct list of users without pagination)
    if (Array.isArray(data)) {
      // Converting array response to paginated format
      // Convert array response to expected format
      return {
        items: data,
        total: data.length,
        page: params.page || 1,
        limit: params.limit || 10,
        pages: Math.max(1, Math.ceil(data.length / (params.limit || 10)))
      };
    }

    return data;
  } catch (error) {
    console.error('Error fetching user list:', error);
    throw error;
  }
}

/**
 * Approve or reject a user
 * @param userId - The ID of the user to approve/reject
 * @param approve - Set to true to approve, false to reject
 * @returns The updated user object
 */
export async function approveUser(userId: number, approve: boolean = true): Promise<UserList> {
  try {
    console.log(`${approve ? 'Approving' : 'Rejecting'} user with ID: ${userId}`);

    // Build the endpoint with query parameter
    const queryParams = new URLSearchParams();
    queryParams.append('approve', approve.toString());
    const endpoint = `${API_ENDPOINTS.ADMIN.USERS.APPROVE_USER(userId)}?${queryParams.toString()}`;

    // Make the API call
    const response = await authenticatedAdminRequest(endpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      // Handle specific error codes
      if (response.status === 403) {
        console.error('Permission denied: You may not have admin rights or cannot change this user');
        throw new Error(`Permission denied: Cannot ${approve ? 'approve' : 'reject'} this user. You may not have sufficient permissions or this user cannot be modified.`);
      } else if (response.status === 401) {
        console.error('Authentication error: Your session may have expired');
        throw new Error('Authentication error: Please log in again.');
      } else {
        // Try to get more details from the response
        try {
          const errorData = await response.json();
          if (errorData && errorData.detail) {
            throw new Error(`Failed to ${approve ? 'approve' : 'reject'} user: ${errorData.detail}`);
          }
        } catch (parseError) {
          // If we can't parse the error, use the status code
          throw new Error(`Failed to ${approve ? 'approve' : 'reject'} user: ${response.status} ${response.statusText}`);
        }
      }
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error ${approve ? 'approving' : 'rejecting'} user:`, error);



    throw error;
  }
}


