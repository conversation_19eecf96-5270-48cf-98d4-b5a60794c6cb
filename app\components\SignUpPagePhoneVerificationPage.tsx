"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import { getTranslation } from "../i18n/translations";
import { signupService } from "../services/signupService";
import { useAuth } from "../context/ApiAuthContext";

// Client component that receives the lng parameter directly
export default function PhoneVerificationPage({ lng }: { lng: string }) {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  // Use the t function from our static translations
  const t = (key: string) => getTranslation(key, lng);
  const [countryCode, setCountryCode] = useState("US");
  const [phonePrefix, setPhonePrefix] = useState("+1");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [error, setError] = useState("");

  // Flow validation - ensure user follows correct sequence
  useEffect(() => {
    // If user is already authenticated, redirect to user journey
    if (isAuthenticated) {
      router.push(`/${lng}/user-journey`);
      return;
    }

    // Check if user came from signup page or is accessing directly
    // If they have any signup data already, they might be returning to this step
    const existingPhoneData = signupService.getPhoneData();
    const existingUserDetails = signupService.getUserDetails();

    // Allow access to phone verification page if:
    // 1. User has no data (coming from signup page for the first time)
    // 2. User has phone data (returning to this step)
    // 3. User has user details but no phone data (going backwards in the flow)
    // Only redirect to signup if user has user details but is trying to skip phone verification
    // This shouldn't happen in normal flow, but prevents users from jumping ahead

    // No need to redirect back to signup - users can access this page directly from signup
  }, [isAuthenticated, lng, router]);

  function handleSubmit() {
    // Basic validation
    if (!phoneNumber.trim()) {
      setError(t("phone_number_error"));
      return;
    }

    // Store phone data in the signup service
    // We pass both the country code (US, UK, etc.) and the phone number with prefix
    signupService.setPhoneData(countryCode, phonePrefix + phoneNumber);

    // Redirect to user details form
    router.push(`/${lng}/user-details`);
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-white">
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md p-6 sm:p-8 md:p-10 space-y-6 sm:space-y-8 bg-white border border-gray-200 rounded-2xl sm:rounded-3xl shadow-md">
        <div className="text-center">
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-10 sm:h-12 md:h-14 w-auto rounded-xl overflow-hidden"
            />
          </div>

          <p className="mb-4 sm:mb-6 md:mb-8 text-gray-800 px-2 sm:px-4 text-base sm:text-lg">
            {t("sign_up_welcome")}
          </p>

          <h2 className="text-xl sm:text-2xl font-medium text-gray-800 mb-4 sm:mb-6 md:mb-8 px-2 sm:px-4">
            {t("phone_verification_title")}
          </h2>

          {error && (
            <div className="p-3 sm:p-4 mb-4 sm:mb-6 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 rounded-md text-red-500 text-xs sm:text-sm">
              {error}
            </div>
          )}

          <div className="px-2 sm:px-4 mb-6 sm:mb-8">
            <div className="space-y-3 sm:space-y-4">
              <div className="flex space-x-1">
                <select
                  value={countryCode}
                  onChange={(e) => {
                    setCountryCode(e.target.value);
                    // Update phone prefix based on country code
                    switch(e.target.value) {
                      case "US": setPhonePrefix("+1"); break;
                      case "UK": setPhonePrefix("+44"); break;
                      case "DE": setPhonePrefix("+49"); break;
                      case "FR": setPhonePrefix("+33"); break;
                      case "IN": setPhonePrefix("+91"); break;
                      case "CH": setPhonePrefix("+41"); break; // Switzerland
                      case "AT": setPhonePrefix("+43"); break; // Austria
                      default: setPhonePrefix("+1");
                    }
                  }}
                  className="w-1/4 px-1.5 py-2 sm:py-3 border border-gray-400 rounded-md text-sm sm:text-base text-gray-800"
                  style={{ color: '#333', minWidth: '45px', paddingLeft: '6px', paddingRight: '6px' }}
                >
                  <option value="US" className="text-gray-800">US</option>
                  <option value="UK" className="text-gray-800">UK</option>
                  <option value="DE" className="text-gray-800">DE</option>
                  <option value="FR" className="text-gray-800">FR</option>
                  <option value="IN" className="text-gray-800">IN</option>
                  <option value="CH" className="text-gray-800">CH</option>
                  <option value="AT" className="text-gray-800">AT</option>
                </select>
                <div className="w-1/5 px-1 sm:px-2 py-2 sm:py-3 border border-gray-400 rounded-md text-sm sm:text-base text-gray-800 flex items-center justify-center">
                  {phonePrefix}
                </div>
                <input
                  type="tel"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  placeholder={t("phone_number_placeholder")}
                  className="flex-1 px-2 sm:px-4 py-2 sm:py-3 border border-gray-400 rounded-md text-sm sm:text-base placeholder-gray-500"
                  required
                  // Remove the "+" if the user enters it (since we're already showing the prefix)
                  onBlur={(e) => {
                    if (e.target.value.startsWith('+')) {
                      setPhoneNumber(e.target.value.substring(1));
                    }
                  }}
                />
              </div>

              <p className="text-xs sm:text-sm text-gray-600 text-left mt-1 sm:mt-2">
                {t("phone_number_info")}
              </p>
            </div>
          </div>

          <button
            onClick={handleSubmit}
            className="w-3/4 sm:w-2/3 md:w-1/2 py-2 px-4 sm:px-6 bg-[#52bcc3] text-white text-sm sm:text-base md:text-lg rounded-full font-medium hover:bg-[#3da8af] transition-colors mb-3"
          >
            {t("continue_button")}
          </button>

          <div className="text-center text-xs sm:text-sm md:text-base text-gray-700">
            {t("have_account")} <Link href={`/${lng}/login`} className="text-[#52bcc3] font-medium hover:underline">
              {t("sign_in_link")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
