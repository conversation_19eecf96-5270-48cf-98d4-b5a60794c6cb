import UserDetailsPage from "../../components/SignUpPageUserDetailsPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Complete Your Profile - Lumalife",
    description: "Complete your profile information"
  };
}

// Server component that passes the lng parameter to the client component
export default async function UserDetails({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <UserDetailsPage lng={lng} />;
}

