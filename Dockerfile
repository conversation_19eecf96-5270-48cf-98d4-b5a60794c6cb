# Use official Node.js image as the base
FROM node:20-alpine as builder

# Set working directory
WORKDIR /app

# Set build arguments for environment variables
ARG DOCKER_BUILD=true
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_WS_URL

# Set environment variables for the build process
ENV DOCKER_BUILD=${DOCKER_BUILD}
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL}

# Copy package.json and package-lock.json
COPY package.json .

# Install dependencies
RUN npm install --production=false

# Copy the rest of the application code
COPY . .

# Create .env file with build arguments
RUN echo "NODE_ENV=production" > .env \
    && echo "NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}" >> .env \
    && echo "NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL}" >> .env \
    && cat .env

# Build the Next.js app with the environment variables
RUN npm run build

# Production image
FROM node:20-alpine as runner
WORKDIR /app

# Copy only necessary files from builder
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/static ./static
COPY --from=builder /app/scripts ./scripts
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.js ./next.config.js
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.env ./.env
# We'll use environment variables from the .env file at runtime

# Set build arguments for environment variables (for potential runtime use)
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_WS_URL

# Set environment variables for runtime
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL}

# Expose port
EXPOSE 3000

# Run the copy-env script and start the Next.js app
# This will use the environment variables provided at runtime and build time
CMD ["sh", "-c", "mkdir -p ./static && npm run copy-env && echo 'Starting Next.js app with environment variables:' && grep -v 'SECRET\\|PASSWORD\\|KEY' .env && npm start"]
