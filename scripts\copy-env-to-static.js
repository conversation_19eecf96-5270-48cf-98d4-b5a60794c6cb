// This script copies environment variables to the static directory
// It's meant to be run during the build process

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env file
dotenv.config();

// Create the static directory if it doesn't exist
const staticDir = path.join(__dirname, '../static');
if (!fs.existsSync(staticDir)) {
  fs.mkdirSync(staticDir, { recursive: true });
}

// Create the env-config.js file with the environment variables
const envConfigPath = path.join(staticDir, 'env-config.js');

// Helper function to ensure URL is provided
const ensureUrl = (url, type) => {
  if (!url) {
    // Check if we have a .env file with the variables
    try {
      if (fs.existsSync('.env')) {
        // Checking .env file for URL
        const envContent = fs.readFileSync('.env', 'utf8');
        const envLines = envContent.split('\n');
        const varName = type === 'API' ? 'NEXT_PUBLIC_API_URL' : 'NEXT_PUBLIC_WS_URL';
        const envVar = envLines.find(line => line.startsWith(`${varName}=`));

        if (envVar) {
          const envValue = envVar.split('=')[1].trim();
          // Found URL in .env file
          return envValue.replace(/\/+$/, ''); // Remove trailing slashes
        }
      }
    } catch (err) {
      // Silently handle .env file read errors
    }

    // If we're in a Docker build, use production URLs
    if (process.env.NODE_ENV === 'production' || process.env.DOCKER_BUILD === 'true') {
      // WARNING: URL not set during build, using production value
      return type === 'API' ? 'https://api.lumalife.de' : 'ws://api.lumalife.de';
    }

    // In development, provide a warning but continue with a default
    // WARNING: URL not set, using default development value
    return type === 'API' ? 'http://localhost:8000' : 'ws://localhost:8000';
  }
  return url.replace(/\/+$/, ''); // Remove trailing slashes
};

// Get API URL from environment variable
const apiUrl = ensureUrl(process.env.NEXT_PUBLIC_API_URL, 'API');

// Get WebSocket URL and ensure it uses the ws:// protocol
let wsUrl = ensureUrl(process.env.NEXT_PUBLIC_WS_URL, 'WS');

// Convert http:// to ws:// and https:// to wss:// if needed
if (wsUrl.startsWith('http://')) {
  wsUrl = wsUrl.replace('http://', 'ws://');
  // Converted WebSocket URL from http:// to ws://
} else if (wsUrl.startsWith('https://')) {
  wsUrl = wsUrl.replace('https://', 'wss://');
  // Converted WebSocket URL from https:// to wss://
}

// Environment URLs configured

const envConfigContent = `// This file is auto-generated during build
// It injects environment variables into the window object
// for use in static HTML files

window.API_BASE_URL = "${apiUrl}";
window.WS_BASE_URL = "${wsUrl}";

// Environment variables loaded
`;

fs.writeFileSync(envConfigPath, envConfigContent);

// Environment configuration complete
