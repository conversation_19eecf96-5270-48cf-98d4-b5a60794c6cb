// Static translations to ensure server/client consistency
// This avoids hydration mismatches by using the same translations on both sides

export const translations = {
  en: {
    app_name: "Lumalife",
    welcome: "Welcome to Lumalife",
    app_description: "Your personal assistant for healthier aging.",
    sign_up_welcome: "Welcome to Lumalife, your personal assistant for healthier aging.",
    sign_up_title: "Sign up to get started!",
    sign_up_button: "Sign up",
    login_title: "Login",
    login_welcome: "Welcome back! Please sign in to your account.",
    login_button: "Login",
    have_account: "Already have an account?",
    sign_in_link: "Log in",
    no_account: "Don't have an account?",
    sign_up_link: "Sign up",
    phone_verification_title: "Enter your phone number",
    phone_number_placeholder: "Phone number",
    phone_number_info: "Your phone number is needed to enable the full functionality of your personal assistant. We'll send you a text message to confirm your identity.",
    phone_number_error: "Please enter your phone number",
    continue_button: "Continue",
    email: "Email",
    password: "Password",
    forgot_password: "Forgot password?",
    all_fields_required: "All fields are required",
    language_en: "English",
    language_de: "German",
    language: "Language",
    gender: "GENDER",
    gender_male: "MALE",
    gender_female: "FEMALE",
    invalid_email_format: "Please enter a valid email address",
    password_too_short: "Password must be at least 8 characters",
    password_no_uppercase: "Password must contain at least one uppercase letter",
    password_no_number: "Password must contain at least one number",
    password_no_special: "Password must contain at least one special character",
    password_too_weak: "Password is too weak",
    password_strong: "Password is strong",
    passwords_dont_match: "Passwords do not match",
    confirm_password: "Confirm Password",
    password_requirements: "Password must be at least 8 characters with 1 uppercase letter, 1 number, and 1 special character.",
    invalid_date_format: "Please enter a valid date in DD-MM-YYYY format",
    date_format: "FORMAT: DD-MM-YYYY",
    processing: "Processing your registration",
    confirmation_message: "Thank you for registering with Lumalife. Your registration is being processed.",
    confirmation_team: "The Lumalife Team",
    error_generic: "An error occurred. Please try again.",
    error_invalid_credentials: "Invalid email or password. Please check your credentials and try again.",
    first_name: "First Name",
    last_name: "Last Name",
    birthdate: "Date of Birth",
    birthdate_info: "DATE OF BIRTH",
    email_info: "EMAIL (NEEDED FOR SECURITY)",
    password_info: "PASSWORD",
    confirm_password_info: "CONFIRM PASSWORD",
    feedback_info: "We value your feedback to improve our service.",
    no_contact: "I do not wish to be contacted for marketing purposes.",
    terms_agreement_info: "By clicking Agree and continue, you agree to our Terms of Service and Privacy Policy.",
    agree_continue_button: "Agree and continue",
    terms_service_privacy: "Terms of Service and Privacy Policy",
    user_details_title: "Complete Your Profile",
    user_details_subtitle: "Please provide your personal information to complete your profile.",
    organization_name: "Organization Name (Optional)",
    organization_skip: "You can skip this step if you're not part of an organization.",
    next_button: "Next",
    proceed_to_login: "Your information has been saved locally. You can proceed to login.",
    show_debug: "Show Debug",
    hide_debug: "Hide Debug",
    email_already_registered: "You can proceed to login with your existing account.",
    avatar_selection_title: "Just one more step until your first conversation with Luma. Please let us know if your Luma should be female or male.",
    female_avatar: "Luma (female)",
    male_avatar: "Luma (male)",
    swipe_instruction: "swipe left",
    start_conversation: "Start Conversation",
    create_assistant: "Create my assistant",
    change_avatar: "Change avatar",
    approval_pending_title: "Account pending approval",
    approval_pending_message: "Your account has been registered successfully. An administrator will review your account and approve it shortly. You'll be able to access all features once approved.",
    account_not_approved: "Thank you for registering! Your account is currently awaiting administrator approval. You will receive an email notification once your account has been approved.",

    // Admin-specific translations
    admin_login_title: "Admin Login",
    admin_signup_title: "Admin Signup",
    admin_login_button: "Login as Admin",
    admin_signup_button: "Sign up as Admin",
    admin_login_link: "Login as Admin",
    admin_key: "Admin Key",
    admin_key_placeholder: "Enter admin creation key",
    no_admin_account: "Don't have an admin account?",
    already_have_admin_account: "Already have an admin account?",
    back_to_user_login: "Back to User Login",
    admin_dashboard: "Admin Dashboard",
    welcome_admin: "Welcome, Admin",
    admin_dashboard_description: "This is the admin dashboard where you can manage users, configurations, and more.",
    user_management: "User Management",
    user_management_description: "Manage user accounts, approvals, and permissions.",
    configuration: "Configuration",
    configuration_description: "Manage system settings and configurations.",
    analytics: "Analytics",
    analytics_description: "View system analytics and usage statistics.",
    manage_users: "Manage Users",
    manage_configuration: "Manage Configuration",
    view_analytics: "View Analytics",
    signing_up: "Signing up...",
    error_invalid_admin_key: "Invalid admin creation key. Please check the key and try again.",
    nav_admin: "Admin",
    nav_admin_dashboard: "Admin Dashboard",
    redirecting: "Redirecting...",

    // User status translations
    user_status_approved: "Approved",
    user_status_pending: "Pending",
    user_action_approve: "Approve",
    user_action_unapprove: "Unapprove",
    user_approved_success: "User has been approved successfully.",
    user_unapproved_success: "User has been unapproved successfully.",
    user_approved_error: "Failed to approve user. Please try again.",
    user_unapproved_error: "Failed to unapprove user. Please try again.",
    cannot_modify_admin: "Cannot modify approval status of admin users.",
    permission_denied: "Permission denied: You may not have sufficient permissions or this user cannot be modified.",
    session_expired: "Your session has expired. Please log in again.",

    // Welcome page translations
    welcome_title: "Welcome to Luma Life!",
    welcome_description: "Luma is your personal AI everyday assistant. Click Start to begin your journey.",
    start: "Start",
    starting: "Starting...",
    error_start_failed: "Failed to start. Please try again.",

    // Error messages
    error_avatar_update: "Failed to update avatar selection. Please try again.",
    error_update_failed: "Failed to update. Please try again.",

    // Connection status
    connected: "Connected",
    connecting: "Connecting...",
    disconnected_click_reconnect: "Disconnected (click to reconnect)",
    paused_click_resume: "Paused (click to resume)",

    // Conversation page
    hi_user: "Hi {name}!",
    greeting: "hi",
    start_conversation_prompt: "Click Start conversation to begin the personalisation of your assistant.",

    // Personalization screen
    assistant_ready: "Your personal assistant is ready!",
    click_start_chatting: "Click Start conversation to start chatting with your assistant.",

    // Preferences screen
    remaining_conversation_time: "Remaining conversation time:",
    give_feedback: "Give feedback",
    luma_getting_started: "Luma is getting started...",
    luma_speaking: "Luma is speaking...",
    minute: "minute",
    minutes: "minutes",
    second: "second",
    seconds: "seconds",
    and: "and",

    // Feedback screen
    token_limit_reached: "Your token limit is reached!",
    click_give_feedback: "Click Give feedback to continue using our service.",
    thank_you_feedback: "Thank you for helping to make our product better!",
    click_start_feedback_conversation: "Click Start conversation to begin your feedback conversation.",
    click_submit_feedback: "Click Submit Feedback when you're done with your feedback.",
    submit_feedback: "Submit Feedback"
  },
  de: {
    app_name: "Lumalife",
    welcome: "Willkommen bei Lumalife",
    app_description: "Ihr persönlicher Assistent für gesünderes Altern.",
    sign_up_welcome: "Willkommen bei Luma Life, deiner persönlichen Assistenz-App für mehr Selbstständigkeit im Leben mit Demenz.",
    sign_up_title: "Registriere dich, um loszulegen!",
    sign_up_button: "Registrieren",
    login_title: "Anmelden",
    login_welcome: "Willkommen zurück! Bitte melden Sie sich bei Ihrem Konto an.",
    login_button: "Anmelden",
    logout: "Abmelden",
    have_account: "Hast du bereits einen Account?",
    sign_in_link: "Anmelden",
    no_account: "Haben Sie noch kein Konto?",
    sign_up_link: "Registrieren",
    phone_verification_title: "Bitte gib deine Telefonnummer ein",
    phone_number_placeholder: "Telefonnummer",
    phone_number_info: "Deine Telefonnummer wird benötigt, um die volle Funktionalität deines persönlichen Assistenten zu ermöglichen. Wir senden dir eine SMS, um deine Identität zu bestätigen.",
    phone_number_error: "Bitte gib deine Telefonnummer ein",
    continue_button: "Fortfahren",
    email: "E-Mail",
    password: "Passwort",
    forgot_password: "Passwort vergessen?",
    all_fields_required: "Alle Felder sind erforderlich",
    language_en: "Englisch",
    language_de: "Deutsch",
    language: "Sprache",
    gender: "GESCHLECHT",
    gender_male: "MÄNNLICH",
    gender_female: "WEIBLICH",
    invalid_email_format: "Bitte gib eine gültige E-Mail-Adresse ein",
    password_too_short: "Das Passwort muss mindestens 8 Zeichen lang sein",
    password_no_uppercase: "Das Passwort muss mindestens einen Großbuchstaben enthalten",
    password_no_number: "Das Passwort muss mindestens eine Zahl enthalten",
    password_no_special: "Das Passwort muss mindestens ein Sonderzeichen enthalten",
    password_too_weak: "Das Passwort ist zu schwach",
    password_strong: "Das Passwort ist stark",
    passwords_dont_match: "Die Passwörter stimmen nicht überein",
    confirm_password: "Passwort bestätigen",
    password_requirements: "Das Passwort muss mindestens 8 Zeichen lang sein und mindestens einen Großbuchstaben, eine Zahl und ein Sonderzeichen enthalten.",
    invalid_date_format: "Bitte gib ein gültiges Datum im Format TT-MM-JJJJ ein",
    date_format: "FORMAT: TT-MM-JJJJ",
    processing: "Deine Registrierung wird bearbeitet",
    confirmation_message: "Vielen Dank für deine Registrierung bei Luma Life. Deine Registrierung wird bearbeitet.",
    confirmation_team: "Dein Luma Life Team",
    error_generic: "Ein Fehler ist aufgetreten. Bitte versuche es erneut.",
    error_invalid_credentials: "Ungültige E-Mail oder Passwort. Bitte überprüfe deine Anmeldedaten und versuche es erneut.",
    first_name: "Vorname",
    last_name: "Nachname",
    birthdate: "Geburtsdatum",
    birthdate_info: "GEBURTSDATUM",
    email_info: "Deine E-Mail-Adresse wird aus Sicherheitsgründen benötigt.",
    password_info: "PASSWORT",
    confirm_password_info: "PASSWORT BESTÄTIGEN",
    feedback_info: "Wir möchten mit dir inKontakt treten, um Feedback zu deiner Erfahrung mit unserem Produkt zu sammeln.",
    no_contact: "Ich möchte nicht von Luma Life kontaktiert werden.",
    terms_agreement_info: "Durch Klicken auf Zustimmen und fortfahren stimmst du unseren Nutzungsbedingungen und Datenschutzrichtlinien zu.",
    agree_continue_button: "Zustimmen und fortfahren",
    terms_service_privacy: "Nutzungsbedingungen und Datenschutzrichtlinien",
    user_details_title: "Wie sollen wir dich nennen?",
    user_details_subtitle: "Luma Life ist speziell für Menschen mit Gedächtnisschwierigkeiten konzipiert. Dein Alter hilft uns dabei, deinen Alltagsassistenten optimal für dich personalisieren zu können.",
    organization_name: "Organisationsname (Optional)",
    organization_skip: "Du kannst diesen Schritt überspringen, wenn du nicht Teil einer Organisation bist.",
    next_button: "Weiter",
    proceed_to_login: "Deine Informationen wurden lokal gespeichert. Du kannst dich jetzt anmelden.",
    show_debug: "Debug anzeigen",
    hide_debug: "Debug ausblenden",
    email_already_registered: "Du kannst dich mit deinem bestehenden Konto anmelden.",
    avatar_selection_title: "Nur noch ein Schritt bis zu deinem ersten Gespräch mit Luma. Sag uns bitte nur noch kurz, ob dein Luma weiblich oder männlich sein soll.",
    female_avatar: "Luma (weiblich)",
    male_avatar: "Luma (männlich)",
    swipe_instruction: "nach links wischen",
    start_conversation: "Gespräch beginnen",
    create_assistant: "Meinen Assistenten erstellen",
    change_avatar: "Avatar ändern",
    approval_pending_title: "Dein Account muss noch bestätigt werden",
    approval_pending_message: "Dein Account wurde erfolgreich registriert. Wir überprüfen deine Angaben und bestätigen deinen Zugang zu Luma in Kürze. Sobald dein Zugang bestätigt ist, hast du Zugriff auf alle Features.",
    account_not_approved: "Vielen Dank für deine Registrierung! Dein Account wartet derzeit auf die Bestätigung. Du erhältst eine E-Mail-Benachrichtigung, sobald dein Account bestätigt wurde.",
    approval_checking_status: "Dein Account-Status wird laufend überprüft. Du wirst automatisch zur App weitergeleitet, sobald dein Zugang bestätigt ist.",

    // Admin-specific translations
    admin_login_title: "Admin-Anmeldung",
    admin_signup_title: "Admin-Registrierung",
    admin_login_button: "Als Admin anmelden",
    admin_signup_button: "Als Admin registrieren",
    admin_login_link: "Als Admin anmelden",
    admin_key: "Admin-Schlüssel",
    admin_key_placeholder: "Admin-Erstellungsschlüssel eingeben",
    no_admin_account: "Haben Sie noch kein Admin-Konto?",
    already_have_admin_account: "Haben Sie bereits ein Admin-Konto?",
    back_to_user_login: "Zurück zur Benutzeranmeldung",
    admin_dashboard: "Admin-Dashboard",
    welcome_admin: "Willkommen, Admin",
    admin_dashboard_description: "Dies ist das Admin-Dashboard, in dem Sie Benutzer, Konfigurationen und mehr verwalten können.",
    user_management: "Benutzerverwaltung",
    user_management_description: "Verwalten Sie Benutzerkonten, Genehmigungen und Berechtigungen.",
    configuration: "Konfiguration",
    configuration_description: "Verwalten Sie Systemeinstellungen und Konfigurationen.",
    analytics: "Analytik",
    analytics_description: "Sehen Sie sich Systemanalytik und Nutzungsstatistiken an.",
    manage_users: "Benutzer verwalten",
    manage_configuration: "Konfiguration verwalten",
    view_analytics: "Analytik anzeigen",
    signing_up: "Registrierung läuft...",
    error_invalid_admin_key: "Ungültiger Admin-Erstellungsschlüssel. Bitte überprüfen Sie den Schlüssel und versuchen Sie es erneut.",
    nav_admin: "Admin",
    nav_admin_dashboard: "Admin-Dashboard",
    redirecting: "Weiterleitung...",

    // User status translations
    user_status_approved: "Genehmigt",
    user_status_pending: "Ausstehend",
    user_action_approve: "Genehmigen",
    user_action_unapprove: "Ablehnen",
    user_approved_success: "Benutzer wurde erfolgreich genehmigt.",
    user_unapproved_success: "Benutzer wurde erfolgreich abgelehnt.",
    user_approved_error: "Fehler beim Genehmigen des Benutzers. Bitte versuchen Sie es erneut.",
    user_unapproved_error: "Fehler beim Ablehnen des Benutzers. Bitte versuchen Sie es erneut.",
    cannot_modify_admin: "Der Genehmigungsstatus von Administratoren kann nicht geändert werden.",
    permission_denied: "Zugriff verweigert: Sie haben möglicherweise nicht ausreichende Berechtigungen oder dieser Benutzer kann nicht geändert werden.",
    session_expired: "Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.",

    // Welcome page translations
    welcome_title: "Willkommen bei Luma Life!",
    welcome_description: "Willkommen bei Luma Life, deiner persönlichen Assistenz-App für mehr Selbstständigkeit im Leben mit Demenz.",
    start: "Start",
    starting: "Starten...",
    error_start_failed: "Start fehlgeschlagen. Bitte versuche es erneut.",

    // Error messages
    error_avatar_update: "Aktualisierung der Avatar-Auswahl fehlgeschlagen. Bitte versuche es erneut.",
    error_update_failed: "Aktualisierung fehlgeschlagen. Bitte versuche es erneut.",

    // Connection status
    connected: "Verbunden",
    connecting: "Verbinde...",
    disconnected_click_reconnect: "Getrennt (zum Wiederverbinden klicken)",
    paused_click_resume: "Pausiert (zum Fortsetzen klicken)",

    // Conversation page
    hi_user: "Hallo {name}!",
    greeting: "hallo",
    start_conversation_prompt: "Klicke auf Gespräch beginnen, um die Personalisierung von Luma zu vervollständigen.",

    // Personalization screen
    assistant_ready: "Dein ganz persönlicher Assistent ist nun bereit für dich!",
    click_start_chatting: "Klicke auf Gespräch beginnen, um dich mit Luma zu unterhalten.",

    // Preferences screen
    remaining_conversation_time: "Verbleibende Gesprächszeit:",
    give_feedback: "Feedback geben",
    luma_getting_started: "Luma wird gestartet...",
    luma_speaking: "Luma spricht...",
    minute: "Minute",
    minutes: "Minuten",
    second: "Sekunde",
    seconds: "Sekunden",
    and: "und",

    // Feedback screen
    token_limit_reached: "Entschuldigung, dein Zeitlimit mit deinem Assistenten ist erreicht. ",
    click_give_feedback: "Klicke auf Feedback geben, um unseren Service weiter zu nutzen.",
    thank_you_feedback: "Vielen Dank, dass du uns hilfst, unser Produkt zu verbessern!",
    click_start_feedback_conversation: "Klicke auf Gespräch beginnen, um dein Feedback-Gespräch zu starten.",
    click_submit_feedback: "Klicke auf Feedback absenden, wenn du mit deinem Feedback fertig bist.",
    submit_feedback: "Feedback absenden"
  }
};

// Simple translation function that works on both server and client
export function getTranslation(key: string, lng: string): string {
  return translations[lng as keyof typeof translations]?.[key as keyof (typeof translations)["en"]] || key;
}
