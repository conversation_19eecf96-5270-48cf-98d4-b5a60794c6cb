"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useTranslation } from "../i18n/client";
import { signupService } from "../services/signupService";
// Removed checkEmailExists import - email validation is handled by signup function
import LanguageSwitcher from "./LanguageSwitcher";
import { useAuth } from "../context/ApiAuthContext";

// Client component that receives the lng parameter
export default function UserDetailsPage({ lng }: { lng: string }) {
  const router = useRouter();
  const { t } = useTranslation("common", lng);
  const { isAuthenticated, login } = useAuth();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [dob, setDob] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [error, setError] = useState("");

  // Flow validation - ensure user follows correct sequence
  useEffect(() => {
    // If user is already authenticated, redirect to user journey
    if (isAuthenticated) {
      router.push(`/${lng}/user-journey`);
      return;
    }

    // BYPASS: Comment out phone data validation since we're bypassing phone verification
    // Check if user has phone data (required to access this page)
    // const phoneData = signupService.getPhoneData();

    // if (!phoneData) {
    //   // If no phone data, redirect back to phone verification
    //   router.push(`/${lng}/phone-verification`);
    //   return;
    // }

    // Load existing user details if returning to this page
    const existingUserDetails = signupService.getUserDetails();
    const existingEmail = signupService.getEmail();

    if (existingUserDetails) {
      setFirstName(existingUserDetails.first_name || "");
      // BYPASS: Comment out loading of lastName and dob
      // setLastName(existingUserDetails.last_name || "");
      // setDob(existingUserDetails.birthdate || "");
    }

    if (existingEmail) {
      setEmail(existingEmail);
    }

    // Reset submission states when component mounts to prevent stuck states
    setHasSubmitted(false);
    setIsSubmitting(false);
  }, [isAuthenticated, router, lng]);

  // Function to check password strength - simplified to only check minimum length
  const checkPasswordStrength = useCallback((password: string) => {
    let strength = 0;

    // Check length - only requirement is minimum 8 characters
    if (password.length >= 8) {
      strength = 4; // Set to maximum strength if minimum length is met
    } else {
      strength = 0;
    }

    setPasswordStrength(strength);
  }, []);

  // Check password strength when password changes
  useEffect(() => {
    if (password) {
      checkPasswordStrength(password);
    } else {
      setPasswordStrength(0);
    }
  }, [password, checkPasswordStrength]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);

  const handleSubmit = async () => {
    // Prevent multiple submissions - check both isSubmitting and hasSubmitted
    if (isSubmitting || hasSubmitted) {
      console.log('Signup already in progress or completed, ignoring duplicate request');
      return;
    }

    // Basic validation - BYPASS: removed lastName and dob validation
    // if (!firstName.trim() || !lastName.trim() || !dob || !email.trim() || !password.trim() || !confirmPassword.trim()) {
    if (!firstName.trim() || !email.trim() || !password.trim() || !confirmPassword.trim()) {
      setError("all_fields_required");
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError("invalid_email_format");
      return;
    }

    // Password strength validation - only check for minimum length
    if (password.length < 8) {
      setError("password_too_short");
      return;
    }

    // Password confirmation validation
    if (password !== confirmPassword) {
      setError("error_passwords_dont_match");
      return;
    }

    // Date validation - BYPASS: commented out since dob field is commented out
    // try {
    //   const dateObj = new Date(dob);
    //   if (isNaN(dateObj.getTime())) {
    //     setError("invalid_date_format");
    //     return;
    //   }
    // } catch (e) {
    //   setError("invalid_date_format");
    //   return;
    // }

    // Set submitting state to prevent multiple submissions
    setIsSubmitting(true);
    setHasSubmitted(true);
    setError("");

    try {
      console.log('Proceeding with signup for email:', email);
      console.log('Setting language to:', lng);

      // Store user details in the signup service - BYPASS: using empty values for lastName and dob
      // signupService.setUserDetails(firstName, lastName, dob, email, password);
      signupService.setUserDetails(firstName, "", "", email, password);

      // Store the current language in the signup service
      signupService.setLanguage(lng);

      // BYPASS: Set empty organization name since we're bypassing organization page
      signupService.setOrganizationName("");

      // Submit signup immediately on user-details page
      // The signup function will handle email existence checking internally
      console.log('Submitting signup to backend');
      const result = await signupService.submitSignup();

      if (result && result.success) {
        console.log('Signup successful, attempting to create token by logging in');

        try {
          // Attempt to login immediately after successful signup to create a token
          await login(email, password);
          console.log('Login successful after signup, token created');

          // Clear signup service data now that we have a token
          signupService.reset();

          // Redirect to confirmation page
          router.push(`/${lng}/confirmation`);
        } catch (loginError) {
          console.log('Login failed after signup, but signup was successful. User can login later.');
          console.error('Login error:', loginError);

          // Don't clear signup service data yet - the confirmation page will need it
          // Redirect to confirmation page anyway - it will handle the token creation
          router.push(`/${lng}/confirmation`);
        }
      } else if (result && result.error && result.errorType === 'EMAIL_ALREADY_REGISTERED') {
        setError("email_already_registered");
        // Reset hasSubmitted on email already registered error so user can try with different email
        setHasSubmitted(false);
      } else {
        setError("Signup failed. Please try again.");
        // Reset hasSubmitted on other errors so user can retry
        setHasSubmitted(false);
      }
    } catch (error) {
      console.error('Error checking email:', error);
      setError("error_checking_email");
      // Reset hasSubmitted on error so user can retry
      setHasSubmitted(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-6 bg-white">
      <div className="w-full max-w-md p-8 space-y-4 bg-white border border-gray-200 rounded-3xl shadow-md">
        <div>
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-center mb-4">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-10 w-auto"
            />
          </div>

          <h2 className="text-xl font-medium text-center text-gray-800 mb-4">
            {t("user_details_title")}
          </h2>

          <p className="text-left text-sm text-gray-600 mb-4">
            {t("user_details_subtitle")}
          </p>

          {error && (
            <div className="p-3 mb-4 bg-red-500/10 border border-red-500/30 rounded-md text-red-500 text-sm">
              {(() => {
                // Handle specific known error keys with language support
                if (error === "invalid_email_format") {
                  return lng === "de"
                    ? "Bitte gib eine gültige E-Mail-Adresse ein"
                    : "Please enter a valid email address";
                } else if (error === "all_fields_required") {
                  return lng === "de"
                    ? "Alle Felder sind erforderlich"
                    : "All fields are required";
                } else if (error === "password_too_short") {
                  return lng === "de"
                    ? "Das Passwort muss mindestens 8 Zeichen lang sein"
                    : "Password must be at least 8 characters";
                } else if (error === "error_passwords_dont_match") {
                  return lng === "de"
                    ? "Die Passwörter stimmen nicht überein"
                    : "Passwords do not match";
                } else if (error === "invalid_date_format") {
                  return lng === "de"
                    ? "Bitte gib ein gültiges Datum im Format TT-MM-JJJJ ein"
                    : "Please enter a valid date in DD-MM-YYYY format";
                } else if (error === "email_already_registered") {
                  return lng === "de"
                    ? "Diese E-Mail-Adresse ist bereits registriert. Bitte verwende eine andere E-Mail-Adresse oder melde dich mit deinem bestehenden Konto an."
                    : "This email is already registered. Please use a different email address or login with your existing account.";
                } else if (error === "error_checking_email") {
                  return lng === "de"
                    ? "Fehler beim Überprüfen der E-Mail-Adresse. Bitte versuche es erneut."
                    : "Error checking email address. Please try again.";
                } else if (error.startsWith("Password must be")) {
                  return error;
                }

                // For any other error, try to translate it
                const translated = t(error);
                if (translated !== error) {
                  return translated;
                }

                // If all else fails, return the error as is
                return error;
              })()}
            </div>
          )}

          <div className="space-y-3">
            <div>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder={`${t("first_name")}*`}
                className="w-full px-4 py-2 border border-gray-300 rounded-full text-base"
                required
              />
            </div>

            {/* BYPASS: Comment out lastname field
            <div>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder={`${t("last_name")}*`}
                className="w-full px-4 py-2 border border-gray-300 rounded-full text-base"
                required
              />
            </div>
            */}

            {/* BYPASS: Comment out date of birth field
            <div>
              <p className="text-xs text-gray-600 mt-2 mb-1 font-medium">
                {t("birthdate_info")}
              </p>
              <input
                type="date"
                value={dob}
                onChange={(e) => setDob(e.target.value)}
                placeholder={`${t("birthdate")}*`}
                className="w-full px-4 py-2 border border-gray-300 rounded-full text-base"
                required
              />
              <p className="text-xs text-gray-600 font-medium mt-1">
                {lng === "de" ? "FORMAT: TT-MM-JJJJ" : "FORMAT: DD-MM-YYYY"}
              </p>
            </div>
            */}



            <div>
              <p className="text-xs text-gray-600 mt-2 mb-1 font-medium">
                {t("email_info")}
              </p>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={`${t("email")}*`}
                className="w-full px-4 py-2 border border-gray-300 rounded-full text-base"
                required
              />
            </div>

            <div>
              <p className="text-xs text-gray-600 mt-2 mb-1 font-medium">
                {t("password_info")}
              </p>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder={`${t("password")}*`}
                className="w-full px-4 py-2 border border-gray-300 rounded-full text-base"
                required
              />

              {/* Password strength indicator - simplified */}
              <div className="mt-2 mb-1">
                <div className="h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full ${
                      passwordStrength === 0 ? 'w-0' :
                      'w-full bg-green-500'
                    }`}
                  ></div>
                </div>
              </div>

              {/* Password feedback - using hardcoded values based on language */}
              <p className={`text-xs mt-1 ${
                passwordStrength === 0 ? 'text-gray-600' : 'text-green-500'
              }`}>
                {password.length >= 8
                  ? (lng === "de" ? "Das Passwort ist stark" : "Password is strong")
                  : password
                    ? (lng === "de" ? "Das Passwort muss mindestens 8 Zeichen lang sein" : "Password must be at least 8 characters")
                    : ""}
              </p>
            </div>

            {/* Confirm Password Field */}
            <div>
              <p className="text-xs text-gray-600 mt-2 mb-1 font-medium">
                {t("confirm_password_info")}
              </p>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder={`${t("confirm_password")}*`}
                className={`w-full px-4 py-2 border rounded-full text-base ${
                  confirmPassword && password !== confirmPassword
                    ? 'border-red-500'
                    : 'border-gray-300'
                }`}
                required
              />
              {confirmPassword && password !== confirmPassword && (
                <p className="text-xs text-red-500 mt-1">
                  {t("error_passwords_dont_match")}
                </p>
              )}
            </div>

            {/* BYPASS: Comment out feedback contact text
            <p className="text-xs text-gray-600 mt-2">
              {t("feedback_info")}
            </p>
            */}

            {/* BYPASS: Comment out terms and conditions checkbox
            <div className="flex items-start mt-2">
              <input
                type="checkbox"
                id="terms"
                checked={agreeTerms}
                onChange={(e) => setAgreeTerms(e.target.checked)}
                className="mt-1 mr-2"
              />
              <label htmlFor="terms" className="text-xs text-gray-600 text-left">
                {t("no_contact")}
              </label>
            </div>
            */}

            <p className="text-xs text-gray-600 mt-2">
              {lng === "de" ? (
                <>
                  Durch Klicken auf <span className="font-bold">Zustimmen und fortfahren</span> stimmst du unseren{" "}
                  <Link href="https://lumalife.de/en/datenschutz/" target="_blank" className="text-[#52bcc3] hover:underline">Datenschutzrichtlinien</Link> zu.
                </>
              ) : (
                <>
                  By clicking <span className="font-bold">Agree and continue</span> you agree to our{" "}
                  <Link href="https://lumalife.de/en/datenschutz/" target="_blank" className="text-[#52bcc3] hover:underline">Privacy Policy</Link>.
                </>
              )}
            </p>

            <div className="pt-2">
              <button
                onClick={handleSubmit}
                disabled={isSubmitting || hasSubmitted}
                className={`w-full py-3 px-6 text-white text-base rounded-full font-medium transition-colors ${
                  (isSubmitting || hasSubmitted) ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#52bcc3] hover:bg-[#3da8af]'
                }`}
                style={{ pointerEvents: (isSubmitting || hasSubmitted) ? 'none' : 'auto' }}
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t("checking")}...
                  </span>
                ) : hasSubmitted ? (
                  <span className="flex items-center justify-center">
                    {lng === "de" ? "Verarbeitung..." : "Processing..."}
                  </span>
                ) : (
                  t("agree_continue_button")
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
