{"app_name": "Lumalife", "welcome": "Welcome to Lumalife", "app_description": "Your personal assistant for healthier aging.", "welcome_title": "Welcome to Luma Life!", "welcome_description": "<PERSON><PERSON> is your personal AI everyday assistant. Click <strong>Start</strong> to begin your journey.", "start": "Start", "starting": "Starting...", "error_start_failed": "Start failed. Please try again.", "sign_in_info": "Sign in with any email containing \"user\" (e.g., <EMAIL>) and any password.", "footer_copyright": "© {{year}} Chatbot App", "nav_home": "Home", "nav_dashboard": "Dashboard", "nav_sign_in": "Sign In", "nav_sign_up": "Sign Up", "nav_sign_out": "Sign Out", "logout": "Logout", "sign_in_title": "Sign In", "sign_in_welcome": "Welcome back! Please sign in to your account.", "sign_in_button": "Sign In", "signing_in": "Signing in...", "forgot_password": "Forgot password?", "no_account": "Don't have an account?", "sign_up_link": "Sign up", "sign_up_title": "Sign up to get started!", "sign_up_welcome": "Welcome to <PERSON><PERSON><PERSON>, your personal assistant for healthier aging.", "sign_up_button": "Sign up", "phone_verification_title": "Enter your phone number", "user_details_title": "Finish signing up", "user_details_subtitle": "How should we call you?", "continue_button": "Continue", "agree_continue_button": "Agree and continue", "processing": "Processing...", "checking": "Checking", "phone_number_placeholder": "Phone number", "phone_number_info": "Your phone number is needed to enable the full functionality of your personal assistant. We'll send you a text message to confirm your identity.", "phone_number_error": "Please enter your phone number", "first_name": "First name", "last_name": "Last name", "birthdate": "Birthdate", "birthdate_info": "Lumalife is built for healthy ageing. Your birthdate helps personalize your initial Lumalife data.", "email_info": "Your email is needed for security reasons.", "password_info": "PASSWORD", "confirm_password_info": "CONFIRM PASSWORD", "feedback_info": "We would like to get in touch with you to collect feedback on your experience with our product.", "no_contact": "I don't want to be contacted by <PERSON><PERSON><PERSON>.", "terms_agreement_info": "By selecting Agree and continue below, I agree to Lumalife's Terms of Service and Privacy Policy.", "terms_service_privacy": "Terms of Service and Privacy Policy", "all_fields_required": "All fields marked with * are required", "organization_question": "Are you joining us through an organisation, for example a care center or nursing service?", "organization_name": "Organisation name", "organization_alzheimer_schweiz": "Alzheimer Switzerland", "organization_deutsche_alzheimer": "German Alzheimer Society", "organization_dzne": "DZNE", "organization_none": "None", "next_button": "Next", "organization_skip": "Just click Next if you are not joining us through an organisation.", "confirmation_message": "It's fantastic to meet you! We are just checking a few things about your sign up request. This process might take up to one day. Hang tight and please check your email inbox for your confirmation.", "confirmation_team": "Your Luma Life Team", "login_title": "Sign In", "login_welcome": "Sign in to your account to continue your journey to healthier aging.", "login_button": "Log in", "verify_title": "Verify Your Phone Number", "verify_info": "We've sent a verification code to your phone. Please enter it below to verify your account.", "verification_code_placeholder": "Verification code", "verification_code_error": "Please enter the verification code", "verify_button": "Verify", "verifying": "Verifying...", "resend_code": "Didn't receive a code?", "resend_button": "Resend", "creating_account": "Creating account...", "have_account": "Already have an account?", "sign_in_link": "Log in", "dashboard_title": "Dashboard", "welcome_back": "Welcome back, {{email}}!", "dashboard_subtitle": "Your Dashboard", "dashboard_description": "This is a simple dashboard page. No complex components or data loading.", "loading": "Loading...", "email": "Email", "password": "Password", "confirm_password": "Confirm Password", "error_invalid_credentials": "Invalid email or password", "error_passwords_dont_match": "Passwords do not match", "error_generic": "Something went wrong. Please try again.", "email_already_registered": "This email is already associated with an account.", "login_instead": "Log in to your existing account instead", "language": "Language", "language_en": "English", "language_de": "German", "avatar_selection_title": "Who would you like to become your personal assistant?", "female_avatar": "Female Avatar", "male_avatar": "Male Avatar", "swipe_instruction": "swipe left", "start_conversation": "Start Conversation", "create_assistant": "Create my assistant", "change_avatar": "Change avatar", "approval_pending_title": "Account pending approval", "approval_pending_message": "Your account has been registered successfully. An administrator will review your account and approve it shortly. You'll be able to access all features once approved.", "account_not_approved": "Your account is awaiting administrator approval", "approval_checking_status": "We're automatically checking your approval status. You'll be redirected once your account is approved.", "completion_title": "Thank you for using Luma Life!", "completion_proud": "We are proud to have you onboard.", "completion_feedback": "Your feedback is very valuable to us for improving our product.", "completion_journey": "This concludes the prototype journey. You will be the first to know when our full product launches!", "completion_team": "Your Luma Life Team", "completion_copyright": "© 2025 Luma Life"}