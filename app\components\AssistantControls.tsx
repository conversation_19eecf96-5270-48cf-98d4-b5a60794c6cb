"use client";

import { useState, useEffect, useCallback } from "react";
import { getTranslation } from "../i18n/translations";

// Utility functions for WebSocket controls
export const webSocketUtils = {
  /**
   * Checks if the WebSocket is closed or closing
   * @returns boolean
   */
  isWebSocketClosed: (): boolean => {
    const socket = (window as any).assistantSocket;
    return !socket || socket.readyState === WebSocket.CLOSED || socket.readyState === WebSocket.CLOSING;
  },

  /**
   * Gets the current WebSocket connection status
   * @param isConnected - Whether the component thinks the connection is established
   * @param isPaused - Whether the conversation is paused
   * @returns "connected" | "connecting" | "disconnected" | "paused"
   */
  getConnectionStatus: (
    isConnected: boolean,
    isPaused: boolean
  ): "connected" | "connecting" | "disconnected" | "paused" => {
    const socket = (window as any).assistantSocket;

    if (isPaused) {
      return "paused";
    } else if (isConnected && socket && socket.readyState === WebSocket.OPEN) {
      return "connected";
    } else if (socket && socket.readyState === WebSocket.CONNECTING) {
      return "connecting";
    } else {
      return "disconnected";
    }
  },

  /**
   * Gets the appropriate styles for the connection indicator
   * @param status - The current connection status
   * @param t - Translation function
   * @returns Object with indicator styles, animation, text, and textClass
   */
  getConnectionIndicatorStyles: (
    status: "connected" | "connecting" | "disconnected" | "paused",
    t: (key: string) => string
  ) => {
    switch (status) {
      case "connected":
        return {
          indicator: "inline-block w-3 h-3 rounded-full mr-2 bg-green-500",
          animation: "",
          text: t("connected") || "Connected",
          textClass: "font-medium text-green-700"
        };
      case "connecting":
        return {
          indicator: "inline-block w-3 h-3 rounded-full mr-2 bg-yellow-500",
          animation: "",
          text: "Please wait...",
          textClass: "font-medium text-yellow-700"
        };
      case "paused":
        return {
          indicator: "inline-block w-3 h-3 rounded-full mr-2 bg-blue-500",
          animation: "",
          text: "Paused",
          textClass: "font-medium text-blue-700"
        };
      case "disconnected":
        return {
          indicator: "inline-block w-3 h-3 rounded-full mr-2 bg-yellow-500",
          animation: "",
          text: "Please wait...",
          textClass: "font-medium text-yellow-700"
        };
    }
  },

  /**
   * Determines if buttons should be enabled based on connection status
   * @param status - The current connection status
   * @returns Object with button enable states
   */
  getButtonStates: (
    status: "connected" | "connecting" | "disconnected" | "paused"
  ) => {
    return {
      micEnabled: status === "connected",
      stopEnabled: status === "connected",
      reconnectEnabled: status === "disconnected",
      resumeEnabled: status === "paused"
    };
  }
};

interface WebSocketControlsProps {
  lng: string;
  isRecording: boolean;
  isConnected: boolean;
  onMicClick: () => void;
  onStopClick: () => void;
  onReconnectClick: () => void;
  isPaused?: boolean; // New prop to track if conversation is paused
}

/**
 * Reusable component for WebSocket controls including mic button, stop button, and connection status
 */
export default function WebSocketControls({
  lng,
  isRecording,
  isConnected,
  onMicClick,
  onStopClick,
  onReconnectClick,
  isPaused = false
}: WebSocketControlsProps) {
  const [connectionStatus, setConnectionStatus] = useState<"connected" | "connecting" | "disconnected" | "paused">("connecting");

  // Translate function
  const t = (key: string) => getTranslation(key, lng);

  // Update connection status based on isConnected and isPaused props
  useEffect(() => {
    const socket = (window as any).assistantSocket;
    let newStatus: "connected" | "connecting" | "disconnected" | "paused";

    // Handle paused state first - this is the most important
    if (isPaused) {
      newStatus = "paused";
    }
    // Handle connected state
    else if (isConnected && socket && socket.readyState === WebSocket.OPEN) {
      newStatus = "connected";
    }
    // Handle connecting state
    else if (socket && socket.readyState === WebSocket.CONNECTING) {
      newStatus = "connecting";
    }
    // Handle closed/failed connection
    else if (socket && (socket.readyState === WebSocket.CLOSED || socket.readyState === WebSocket.CLOSING)) {
      newStatus = "disconnected";
    }
    // No socket exists yet - this is the initial state, show connecting
    else if (!socket) {
      newStatus = "connecting";
    }
    // Fallback to disconnected for any other state
    else {
      newStatus = "disconnected";
    }

    setConnectionStatus(newStatus);
  }, [isConnected, isPaused]);

  // Get styles using the utility function
  const styles = webSocketUtils.getConnectionIndicatorStyles(connectionStatus, t);

  // Wrap the onMicClick handler to ensure audio context is initialized
  const handleMicClick = async () => {
    try {
      // Initialize audio context from user gesture
      if (typeof window !== 'undefined') {
        const audioUtils = await import('../utils/audioStreamingStatic');
        await audioUtils.initAudioContext(true);
      }
      
      // Then call the original handler
      if (onMicClick) onMicClick();
    } catch (err) {
      console.error('Error initializing audio context from mic click:', err);
      // Still try to call the original handler
      if (onMicClick) onMicClick();
    }
  };

  return (
    <>
      {/* Connection status indicator */}
      <div className="flex items-center justify-center mb-3 sm:mb-4">
        <div
          className="flex items-center text-[10px] sm:text-xs bg-gray-100 px-2 sm:px-3 py-0.5 sm:py-1 rounded-full shadow-md border border-gray-200"
          title={
            connectionStatus === "paused"
              ? "Paused"
              : connectionStatus === "disconnected"
              ? "Disconnected"
              : connectionStatus === "connecting"
              ? "Wait, it is connecting"
              : "Connected"
          }
        >
          <span id="connection-indicator" className={styles.indicator} dangerouslySetInnerHTML={{ __html: styles.animation }}></span>
          <span id="connection-status" className={styles.textClass}>{styles.text}</span>
        </div>
      </div>

      {/* Control buttons - mic and stop */}
      <div className="flex justify-center space-x-3 sm:space-x-4 mb-4 sm:mb-6">
        <button
          className={`${
            connectionStatus === "connected"
              ? (isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-[#52bcc3] hover:bg-[#3da8af]')
              : 'bg-gray-400 cursor-not-allowed'
          } text-white rounded-full p-2 sm:p-3 font-medium shadow-md flex items-center justify-center transition-all duration-300 relative w-10 h-10 sm:w-12 sm:h-12 ${
            connectionStatus === "connected" ? 'cursor-pointer' : 'cursor-not-allowed'
          }`}
          onClick={connectionStatus === "connected" ? handleMicClick : undefined}
          disabled={connectionStatus !== "connected"}
          aria-label={
            connectionStatus !== "connected"
              ? "Microphone disabled - not connected"
              : (isRecording ? "Mute microphone" : "Unmute microphone")
          }
          title={
            connectionStatus !== "connected"
              ? "Connect to WebSocket first to use microphone"
              : (isRecording ? "Click to mute microphone" : "Click to unmute microphone")
          }
        >
          {isRecording ? (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
              <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 01-7.5 0V4.5z" />
              <path d="M6 10.5a.75.75 0 01.75.75v1.5a5.25 5.25 0 1010.5 0v-1.5a.75.75 0 011.5 0v1.5a6.751 6.751 0 01-6 6.709v2.291h3a.75.75 0 010 1.5h-7.5a.75.75 0 010-1.5h3v-2.291a6.751 6.751 0 01-6-6.709v-1.5A.75.75 0 016 10.5z" />
            </svg>
          ) : (
            <div className="relative">
              {/* Microphone icon */}
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
                <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 01-7.5 0V4.5z" />
                <path d="M6 10.5a.75.75 0 01.75.75v1.5a5.25 5.25 0 1010.5 0v-1.5a.75.75 0 011.5 0v1.5a6.751 6.751 0 01-6 6.709v2.291h3a.75.75 0 010 1.5h-7.5a.75.75 0 010-1.5h3v-2.291a6.751 6.751 0 01-6-6.709v-1.5A.75.75 0 016 10.5z" />
              </svg>
              {/* Diagonal line through microphone */}
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                <div className="w-[110%] h-0.5 bg-white rotate-45 rounded-full"></div>
              </div>
            </div>
          )}
        </button>

        {/* Second button - WebSocket action button (always visible, disabled when not connected) */}
        <button
          className={`${
            // Enable for connected (stop), paused (resume), and disconnected (reconnect)
            connectionStatus === "connected" && !isPaused
              ? "bg-red-500 hover:bg-red-600 cursor-pointer"  // Stop button when connected
              : connectionStatus === "paused"
              ? "bg-green-500 hover:bg-green-600 cursor-pointer"  // Resume button when paused
              : connectionStatus === "disconnected"
              ? "bg-blue-500 hover:bg-blue-600 cursor-pointer"  // Reconnect button when disconnected
              : "bg-gray-400 cursor-not-allowed"  // Disabled only for connecting state
          } text-white rounded-full p-2 sm:p-3 font-medium shadow-md transition-all duration-300 w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center`}
          onClick={
            connectionStatus === "connected" && !isPaused
              ? onStopClick  // Stop action when connected
              : connectionStatus === "paused"
              ? onReconnectClick   // Resume action when paused
              : connectionStatus === "disconnected"
              ? onReconnectClick   // Reconnect action when disconnected
              : undefined  // No action only for connecting state
          }
          disabled={connectionStatus === "connecting"}
          aria-label={
            connectionStatus === "connected" && !isPaused
              ? "Close conversation"
              : connectionStatus === "paused"
              ? "Resume conversation"
              : connectionStatus === "disconnected"
              ? "Reconnect"
              : "Button disabled - connecting"
          }
          title={
            connectionStatus === "connected" && !isPaused
              ? "Click to close the conversation"
              : connectionStatus === "paused"
              ? "Click to resume the conversation"
              : connectionStatus === "disconnected"
              ? "Click to reconnect"
              : "Please wait - connecting to WebSocket..."
          }
        >
          {connectionStatus === "connected" && !isPaused ? (
            // Stop/Close icon when connected
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            // Play/Connect icon for all other states (paused, disconnected, connecting)
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
            </svg>
          )}
        </button>
      </div>
    </>
  );
}
