"use client";

import { useState, useCallback } from "react";
import { getTranslation } from "../i18n/translations";
import DynamicAvatar from "./AssistantAvatar";

// Component for feedback start screen
export default function FeedbackStartScreen({
  lng,
  avatarGender,
  onComplete
}: {
  lng: string;
  avatarGender: "male" | "female";
  onComplete: () => void;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use the t function from our static translations
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Handle start conversation button click
  const handleStartConversation = useCallback(() => {
    setIsSubmitting(true);

    // Simply call onComplete to move to the next screen
    // without updating the stage
    onComplete();

    setIsSubmitting(false);
  }, [onComplete]);

  return (
    <div className="text-center max-w-xs sm:max-w-sm md:max-w-md mx-auto px-2 sm:px-0">
      {/* Thank you message */}
      <div className="mb-2 sm:mb-3 text-base sm:text-lg font-regular text-gray-800 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] bg-clip-text text-transparent">
        {lng === "de" ? (
          <>
            Vielen Dank, dass du uns hilfst, unser Produkt zu verbessern!
            Klicke auf <span className="font-bold">Gespräch beginnen</span>, um das Feedback-Gespräch zu starten.
          </>
        ) : (
          <>
            Thank you for helping us improve our product!<br />
            Click <span className="font-bold">Start conversation</span> to begin the feedback conversation.
          </>
        )}
      </div>

      {/* No error handling needed */}

      <div className="flex flex-col justify-center items-center">
        {/* Avatar display */}
        <div className="flex justify-center items-center mb-2 sm:mb-3 relative w-full">
          <div className="relative mx-auto w-40 h-40 sm:w-48 sm:h-48 md:w-56 md:h-56 lg:w-64 lg:h-64">
            <DynamicAvatar
              gender={avatarGender}
              isActive={false}
              audioLevel={0}
            />
          </div>
        </div>
      </div>

      <div className="mt-2 sm:mt-3">
        <button
          onClick={handleStartConversation}
          disabled={isSubmitting}
          className={`min-w-[140px] sm:min-w-[160px] md:min-w-[180px] py-2 sm:py-2.5 px-4 sm:px-6 ${
            isSubmitting
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer"
          } text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-3 w-3 sm:h-4 sm:w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t("processing") || "Processing..."}
            </span>
          ) : (
            <span className="font-bold">{t("start_conversation") || "Start conversation"}</span>
          )}
        </button>
      </div>
    </div>
  );
}
