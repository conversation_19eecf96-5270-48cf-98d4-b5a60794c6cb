import { NextRequest, NextResponse } from 'next/server';

// API route to handle fetch requests for time updates
export async function PUT(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    const { remaining_time, userId } = body;
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');

    if (!remaining_time || !token) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    console.log(`Time update: User ${userId || 'unknown'}, remaining time: ${remaining_time}s, source: api`);

    // IMPORTANT FIX: Use the correct backend URL
    // In Docker containers, "localhost" refers to the container itself, not the host
    // If your backend is running in a separate container, you need to use the service name
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 
                       process.env.API_URL || 
                       'https://api.lumalife.de'; // Use the Docker service name instead of localhost

    console.log(`Using backend URL: ${backendUrl}`);

    try {
      // Add a timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);

      const backendResponse = await fetch(`${backendUrl}/users/me`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ remaining_time: parseInt(remaining_time) }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (backendResponse.ok) {
        console.log(`Time update successful for user ${userId || 'unknown'}`);
        return NextResponse.json({ success: true });
      } else {
        console.log(`Backend responded with status ${backendResponse.status}`);
        return NextResponse.json({ error: 'Backend update failed' }, { status: backendResponse.status });
      }
    } catch (backendError) {
      console.error('Error forwarding to backend:', backendError);
      
      // Return success anyway to prevent client retries
      return NextResponse.json({ 
        success: false, 
        error: 'Backend connection failed',
        fallback_used: true
      }, { status: 200 });
    }
  } catch (error) {
    console.error('Error in time update endpoint:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
