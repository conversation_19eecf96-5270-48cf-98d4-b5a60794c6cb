"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useTranslation } from "../i18n/client";
import { signupService } from "../services/signupService";
import { login } from "../services/api";
import LanguageSwitcher from "./LanguageSwitcher";
import { useAuth } from "../context/ApiAuthContext";

// Client component that receives the lng parameter
export default function OrganizationPage({ lng }: { lng: string }) {
  const router = useRouter();
  const { t } = useTranslation("common", lng);
  const { isAuthenticated } = useAuth();
  const [organizationName, setOrganizationName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Flow validation - ensure user follows correct sequence
  useEffect(() => {
    // If user is already authenticated, redirect to user journey
    if (isAuthenticated) {
      router.push(`/${lng}/user-journey`);
      return;
    }

    // Check if user has all required data to access this page
    const phoneData = signupService.getPhoneData();
    const userDetails = signupService.getUserDetails();
    const email = signupService.getEmail();
    const password = signupService.getPassword();

    if (!phoneData) {
      // Missing phone data - redirect to phone verification
      router.push(`/${lng}/phone-verification`);
      return;
    }

    if (!userDetails || !email || !password) {
      // Missing user details - redirect to user details page
      router.push(`/${lng}/user-details`);
      return;
    }

    // Load existing organization name if returning to this page
    const existingOrgName = signupService.getOrganizationName();
    if (existingOrgName) {
      setOrganizationName(existingOrgName);
    }
  }, [isAuthenticated, router, lng]);

  async function handleNext() {
    if (isSubmitting) return;

    setIsSubmitting(true);
    setError("");

    try {
      // Store organization name in the signup service (even if empty)
      signupService.setOrganizationName(organizationName);

      // Store the current language in the signup service
      signupService.setLanguage(lng);

      // Check if we have all the required data for signup
      const phoneData = signupService.getPhoneData();
      const userDetails = signupService.getUserDetails();
      const email = signupService.getEmail();
      const password = signupService.getPassword();

      console.log('Checking if we have all required data for signup:');
      console.log('- Phone data:', phoneData ? 'Present' : 'Missing');
      console.log('- User details:', userDetails ? 'Present' : 'Missing');
      console.log('- Email:', email ? 'Present' : 'Missing');
      console.log('- Password:', password ? 'Present' : 'Missing');
      console.log('- Organization name:', organizationName ? 'Present' : 'Not provided (optional)');
      console.log('- Language:', lng);

      if (!phoneData || !userDetails || !email || !password) {
        console.log('Missing required signup data, redirecting to appropriate page');
        if (!phoneData) {
          router.push(`/${lng}/phone-verification`);
        } else {
          router.push(`/${lng}/user-details`);
        }
        return;
      }

      console.log('All required data found, submitting signup');

      try {
        // Submit signup data to the API
        const result = await signupService.submitSignup();

        if (result && result.success) {

          // Create token by logging in
          try {
            await login(email, password);

            // Check if we have a token
            const token = localStorage.getItem('token');
            if (token) {
              // Redirect to confirmation page for approval polling
              router.push(`/${lng}/confirmation`);
            } else {
              setError('Failed to create token. Please try again.');
              setIsSubmitting(false);
            }
          } catch (loginError) {
            console.error('Failed to create token:', loginError);
            setError('Failed to create token. Please try again.');
            setIsSubmitting(false);
          }
        } else if (result && result.error) {
          console.error('Signup failed:', result);
          setError(result.message || 'Signup failed. Please try again.');
          setIsSubmitting(false);
        }
      } catch (submitError) {
        console.error('Error submitting signup:', submitError);
        setError('Error submitting signup. Please try again.');
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      setError('An unexpected error occurred. Please try again.');
      setIsSubmitting(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-6 bg-white">
      <div className="w-full max-w-md p-8 space-y-6 bg-white border border-gray-200 rounded-3xl shadow-md">
        <div>
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-center mb-6">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-12 w-auto"
            />
          </div>

          <div className="text-center mb-8">
            <h2 className="text-xl font-medium text-gray-800 mb-4">
              {t("organization_question")}
            </h2>
          </div>

          <div className="space-y-6">
            <div>
              <select
                value={organizationName}
                onChange={(e) => setOrganizationName(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-full text-base appearance-none bg-white bg-no-repeat bg-right pr-8"
                style={{ backgroundImage: "url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E\")", backgroundSize: "1.5em 1.5em", backgroundPosition: "right 0.75rem center" }}
              >
                <option value="">{t("organization_name")}</option>
                <option value="alzheimer_schweiz">{t("organization_alzheimer_schweiz")}</option>
                <option value="deutsche_alzheimer">{t("organization_deutsche_alzheimer")}</option>
                <option value="dzne">{t("organization_dzne")}</option>
                <option value="none">{t("organization_none")}</option>
              </select>
            </div>

            {error && (
              <div className="p-3 mb-4 bg-red-500/10 border border-red-500/30 rounded-md text-red-500 text-sm">
                {error}
              </div>
            )}

            <div className="flex justify-center">
              <button
                onClick={handleNext}
                disabled={isSubmitting}
                className={`px-10 py-3 text-white text-lg rounded-full font-medium transition-colors ${
                  isSubmitting ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#52bcc3] hover:bg-[#3da8af]'
                }`}
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t("processing")}
                  </span>
                ) : (
                  t("next_button")
                )}
              </button>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
