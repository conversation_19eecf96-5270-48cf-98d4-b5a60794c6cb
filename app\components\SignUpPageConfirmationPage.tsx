"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "../i18n/client";
import { signupService } from "../services/signupService";
import { logout, getUserProfile, login } from "../services/api";
import { useAuth } from "../context/ApiAuthContext";
import LanguageSwitcher from "./LanguageSwitcher";

// Client component that receives the lng parameter
export default function ConfirmationPage({ lng }: { lng: string }) {
  const router = useRouter();
  const { t } = useTranslation("common", lng);
  const { isAuthenticated, isApproved } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  // Debug state removed
  const [debugData, setDebugData] = useState<any>(null);
  // Removed simulated success state
  const [isApprovalPending, setIsApprovalPending] = useState(false);
  // Add state to track if we're checking approval status
  const [isCheckingApproval, setIsCheckingApproval] = useState(false);
  // Add loading state for initial page load
  const [isInitializing, setIsInitializing] = useState(true);
  // Reference to store the interval ID
  const approvalCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // BYPASS: Ref to track if signup has been attempted to prevent multiple calls
  const signupAttempted = useRef(false);

  // Simple check for already approved users
  useEffect(() => {
    // If user is already authenticated and approved, redirect to user journey
    if (isAuthenticated && isApproved) {
      window.location.href = `/${lng}/user-journey`;
      return;
    }
  }, [isAuthenticated, isApproved, lng]);

  // BYPASS: Disabled problematic useEffect that was resetting isSubmitted and causing duplicate requests
  // Check for token and approval status
  // useEffect(() => {
  //   // First check if we have a token - if not, redirect to organization page
  //   const token = localStorage.getItem('token');
  //   if (!token) {
  //     // Check if we have credentials to potentially create a token
  //     const email = signupService.getEmail();
  //     const password = signupService.getPassword();

  //     if (!email || !password) {
  //       router.push(`/${lng}/login`);
  //       return;
  //     }

  //     // BYPASS: Skip organization page redirect since we're bypassing organization
  //     // If we have credentials but no token, redirect to organization page
  //     // router.push(`/${lng}/organization`);
  //     // return;
  //   }

  //   // Email already registered errors should be caught at the user details page
  //   // This is just a fallback in case something goes wrong
  //   const tempError = localStorage.getItem('temp_auth_error');
  //   if (tempError && tempError.includes('Email already registered')) {
  //     // Clear the error since we're now handling it at the user details page
  //     localStorage.removeItem('temp_auth_error');
  //   }

  //   // Check if user is already authenticated and approved - redirect to user journey
  //   if (isAuthenticated && isApproved) {
  //     window.location.href = `/${lng}/user-journey`;
  //     return;
  //   }

  //   // We already checked for token above, now check if it exists
  //   if (token) {
  //     // Set approval pending but don't set isSubmitted to true
  //     // This allows the signup submission to proceed if needed
  //     setIsApprovalPending(true);
  //     return;
  //   }

  //   // Check if we're coming directly to the confirmation page (not from signup flow)
  //   const isDirectAccess = !signupService.getEmail();

  //   if (!isDirectAccess) {
  //     // BYPASS: Check if signup was already attempted before allowing submission
  //     if (!signupAttempted.current) {
  //       // If we have signup data and haven't attempted signup yet, allow submission
  //       setIsSubmitted(false);
  //     } else {
  //       // If signup was already attempted, don't reset isSubmitted
  //       console.log('Signup already attempted, not resetting isSubmitted');
  //     }
  //   } else {
  //     // For direct access, just set as submitted to prevent signup submission
  //     setIsSubmitted(true);
  //     setIsApprovalPending(true);
  //   }
  // }, [lng, isAuthenticated, isApproved, error]);

  // Handle logout button click
  const handleLogout = async () => {
    try {
      // Clean up audio connections before logout
      if (typeof window !== 'undefined') {
        try {
          const audioUtils = await import('../utils/audioStreamingStatic');
          audioUtils.emergencyLogoutCleanup();
        } catch (cleanupErr) {
          console.log('Error during audio cleanup:', cleanupErr);
        }
      }

      // Clear any "Email already registered" error
      localStorage.removeItem('temp_auth_error');

      await logout();

      // Redirect to login page instead of reloading
      window.location.href = `/${lng}/login`;
    } catch (error) {
      // Clear any "Email already registered" error even if logout fails
      localStorage.removeItem('temp_auth_error');

      // If logout fails, redirect to login page
      window.location.href = `/${lng}/login`;
    }
  };

  // No longer needed - removed

  // BYPASS: Disabled another problematic useEffect that was interfering with signup submission
  // Check if user is authenticated and check approval status
  // useEffect(() => {
  //   try {
  //     // We now only redirect if both conditions are met:
  //     // 1. User is authenticated (token exists)
  //     // 2. User is approved (is_approved flag is true)
  //     if (isAuthenticated && isApproved) {
  //       // Redirect to user journey page
  //       window.location.href = `/${lng}/user-journey`;
  //       return;
  //     }

  //     // If user is authenticated but not approved
  //     if (isAuthenticated && !isApproved) {
  //       setIsApprovalPending(true);
  //       // Also set this to prevent any signup submission attempts
  //       setIsSubmitted(true);
  //     }
  //     // If user is not authenticated
  //     else if (!isAuthenticated) {
  //       // Check if we have credentials before showing the pending approval message
  //       const email = signupService.getEmail();
  //       const password = signupService.getPassword();

  //       if (email && password) {
  //         setIsApprovalPending(true);
  //         // Don't set isSubmitted to true here to allow signup submission
  //       }
  //     }
  //   } catch (error) {
  //     // If there's an error checking authentication status, just continue
  //     // Don't set isSubmitted to true here to allow signup submission
  //   }
  // }, [isAuthenticated, isApproved, lng]);

  // Simplified initialization and polling setup
  useEffect(() => {
    console.log('Confirmation page: Initializing...');

    // Don't start polling if there's an error
    if (error) {
      console.log('Error detected, not starting approval polling:', error);
      setIsInitializing(false);
      return;
    }

    // Set approval pending immediately to show the UI
    setIsApprovalPending(true);
    signupAttempted.current = true;

    // Check if we have a token first
    const token = localStorage.getItem('token');
    if (token) {
      console.log('Token found, starting approval polling');
      setIsInitializing(false);
      startApprovalPolling();
      return;
    }

    // If no token, try to create one using signup service credentials
    const email = signupService.getEmail();
    const password = signupService.getPassword();

    if (email && password) {
      console.log('No token but have credentials, attempting login');
      login(email, password)
        .then(() => {
          console.log('Login successful, starting approval polling');
          signupService.reset(); // Clear credentials after successful login
          setIsInitializing(false);
          startApprovalPolling();
        })
        .catch((loginError) => {
          console.log('Login failed:', loginError);
          // Start polling anyway, maybe the user was already created
          setIsInitializing(false);
          startApprovalPolling();
        });
    } else {
      console.log('No token and no credentials, but starting polling anyway');
      // Start polling anyway, maybe the user was already created
      setIsInitializing(false);
      startApprovalPolling();
    }

    // Cleanup function
    return () => {
      if (approvalCheckIntervalRef.current) {
        console.log('Clearing approval polling interval');
        clearInterval(approvalCheckIntervalRef.current);
        approvalCheckIntervalRef.current = null;
      }
    };
  }, [lng]); // Only depend on lng to prevent re-runs

  // Separate function to handle approval polling
  const startApprovalPolling = () => {
    // Delay the first check slightly to let the page render
    setTimeout(() => {
      checkApprovalStatus();
    }, 1000);

    // Set up interval to check every 10 seconds (less frequent to reduce load)
    approvalCheckIntervalRef.current = setInterval(checkApprovalStatus, 10000);
  };

  // Simplified approval checking function
  const checkApprovalStatus = async () => {
    try {
      setIsCheckingApproval(true);

      const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token available for approval check');
        return;
      }

      const userProfile = await getUserProfile();

      if (userProfile && userProfile.is_approved) {
        console.log('User is approved! Redirecting to user journey');
        // Clear the interval before redirecting
        if (approvalCheckIntervalRef.current) {
          clearInterval(approvalCheckIntervalRef.current);
          approvalCheckIntervalRef.current = null;
        }
        window.location.href = `/${lng}/user-journey`;
      } else {
        console.log('User not yet approved, continuing to wait');
      }
    } catch (error) {
      console.log('Error checking approval status:', error);
      // If there's a 401 error, the user might need to login again
      // But we'll just continue polling for now
    } finally {
      setIsCheckingApproval(false);
    }
  };

  // BYPASS: Removed duplicate useEffect - all initialization is now handled in the main useEffect above

    // BYPASS: The code below is completely disabled since we're using submitSignupWithBypass instead
    // The code below is kept for reference but will never execute
    // const submitSignup = async () => {
    //   try {
    //     // Check if we have all the required data
    //     let phoneData = signupService.getPhoneData();
    //     let userDetails = signupService.getUserDetails();
    //     let email = signupService.getEmail();
    //     let password = signupService.getPassword();
    //     const orgName = signupService.getOrganizationName();

    //     // Attempting to register email

    //     // Collect debug data
    //     setDebugData({
    //       phoneData,
    //       userDetails,
    //       email,
    //       password: password ? '********' : null, // Don't show actual password
    //       orgName
    //     });

    //     // Even if data is missing, don't redirect - just show debug info
    //     if (!phoneData || !userDetails || !email || !password) {
    //       // Add detailed logging to see exactly what's missing
    //       const missingItems = [];
    //       if (!phoneData) missingItems.push('Phone Data');
    //       if (!userDetails) missingItems.push('User Details');
    //       if (!email) missingItems.push('Email');
    //       if (!password) missingItems.push('Password');

    //       // Missing signup data warning

    //       // Try to recover missing data from localStorage
    //       let recoveredData = false;

    //       if (!phoneData) {
    //         try {
    //           const storedPhoneData = localStorage.getItem('signupPhoneData');
    //           if (storedPhoneData) {
    //             phoneData = JSON.parse(storedPhoneData);
    //             recoveredData = true;
    //           }
    //         } catch (e) {
    //           console.error('Failed to recover phone data from localStorage:', e);
    //         }
    //       }

    //       if (!userDetails) {
    //         try {
    //           const storedUserDetails = localStorage.getItem('signupUserDetails');
    //           if (storedUserDetails) {
    //             userDetails = JSON.parse(storedUserDetails);
    //             recoveredData = true;
    //           }
    //         } catch (e) {
    //           console.error('Failed to recover user details from localStorage:', e);
    //         }
    //       }

    //       if (!email) {
    //         try {
    //           const storedEmail = localStorage.getItem('signupEmail');
    //           if (storedEmail) {
    //             email = storedEmail;
    //             recoveredData = true;
    //           }
    //         } catch (e) {
    //           console.error('Failed to recover email from localStorage:', e);
    //         }
    //       }

    //       if (!password) {
    //         try {
    //           const storedPassword = localStorage.getItem('signupPassword');
    //           if (storedPassword) {
    //             password = storedPassword;
    //             recoveredData = true;
    //           }
    //         } catch (e) {
    //           console.error('Failed to recover password from localStorage:', e);
    //         }
    //       }

    //       // Check if we still have missing data
    //       const stillMissingItems = [];
    //       if (!phoneData) stillMissingItems.push('Phone Data');
    //       if (!userDetails) stillMissingItems.push('User Details');
    //       if (!email) stillMissingItems.push('Email');
    //       if (!password) stillMissingItems.push('Password');

    //       if (stillMissingItems.length > 0) {
    //         // Still missing data after recovery attempt
    //         // Don't set an error message, just continue with submission if possible
    //         // If submission fails, the error will be caught and handled
    //       }
    //     }

    //     if (isSubmitted) {
    //       return; // Already submitted
    //     }

    //     setIsSubmitting(true);

    //     try {
    //       // Submit signup data to the API
    //       const result = await signupService.submitSignup();

    //       // Check the response type
    //       if (result) {
    //         // Received API response

    //         // Handle success responses
    //         if (result.success) {
    //           // Check if it's a fallback response (backend unavailable)
    //           if (result.fallback || (result.message && result.message.includes('fallback'))) {
    //             // Using fallback response (backend may be unavailable)
    //             setDebugData({
    //               ...debugData,
    //               backendStatus: 'Unavailable - using fallback response',
    //               result
    //             });
    //           }

    //           // Set as submitted to prevent further submission attempts
    //           setIsSubmitted(true);

    //           // After successful signup, create a token by logging in
    //           if (email && password) {
    //             try {
    //               // Add a small delay before attempting to login to ensure the backend has processed the signup
    //               await new Promise(resolve => setTimeout(resolve, 1000));

    //               try {
    //                 // Try to login to create a token
    //                 await login(email, password);

    //                 // Check if we have a token
    //                 const token = localStorage.getItem('token');
    //                 if (!token) {
    //                   // Login completed but no token was found - this is unusual
    //                 }
    //               } catch (err) {
    //                 // Try one more time with a longer delay
    //                 await new Promise(resolve => setTimeout(resolve, 2000));

    //                 try {
    //                   await login(email, password);
    //                 } catch (secondErr) {
    //                   // Second login attempt failed
    //                 }
    //               }

    //               // Set approval pending state regardless of login success
    //               setIsApprovalPending(true);
    //             } catch (loginError) {
    //               console.error('Error in login process after signup:', loginError);
    //               // Even if login fails, we still consider signup successful
    //               // The user can try logging in later

    //               // Set approval pending state anyway
    //               setIsApprovalPending(true);
    //             }
    //           }
    //         }
    //         // Email already registered errors should be caught at the user details page
    //         // This is just a fallback in case something goes wrong
    //         else if (result.error && result.errorType === 'EMAIL_ALREADY_REGISTERED') {
    //           console.log('Unexpected email already registered error in confirmation page - this should have been caught earlier');

    //           // Just log the error and continue with normal flow
    //           console.log('Continuing with normal flow despite email error');

    //           // Set as submitted to prevent further submission attempts
    //           setIsSubmitted(true);
    //         }
    //       }

    //       // Mark as submitted to prevent duplicate submissions
    //       setIsSubmitted(true);

    //       // Reset signup store after successful submission
    //       signupService.reset();
    //     } catch (submitError) {
    //       // Handle specific error cases
    //       if (submitError instanceof Error) {
    //         console.log('Handling submitError in ConfirmationPage:', submitError.message);

    //         // Email already registered errors should be caught at the user details page
    //         // This is just a fallback in case something goes wrong
    //         if (typeof submitError.message === 'string' &&
    //             (submitError.message.includes('Email already registered') ||
    //             submitError.message.includes('EMAIL_ALREADY_REGISTERED'))) {
    //           console.log('Unexpected email already registered error in confirmation page - this should have been caught earlier');

    //           // Just log the error and continue with normal flow
    //           console.log('Continuing with normal flow despite email error');

    //           // Set as submitted to prevent further submission attempts
    //           setIsSubmitted(true);
    //           return; // Don't proceed with the rest of the function
    //         }

    //         // Handle specific error types
    //         if (typeof submitError.message === 'string') {
    //           // Database connection error - show error but also mark as submitted
    //           if (submitError.message.includes('database connection error')) {
    //             setError(submitError.message);
    //             setIsSubmitted(true);
    //           }
    //           // Timeout error - show error but also mark as submitted
    //           else if (submitError.message.includes('timed out') ||
    //                   submitError.message.includes('SERVER_ERROR')) {
    //             setError('The server is taking too long to respond. Please try again later.');
    //             setIsSubmitted(true);
    //           } else {
    //             // For other errors, don't set simulated success, but mark as submitted to prevent retries
    //             setIsSubmitted(true);
    //             setError(submitError.message);

    //             // Set debug data for development
    //             setDebugData({
    //               error: 'Signup error',
    //               message: submitError.message
    //             });

    //             // For other errors, just throw them to be caught by the outer catch block
    //             throw submitError;
    //           }
    //         } else {
    //           // For non-string error messages, just throw them
    //           throw submitError;
    //         }
    //       } else {
    //         // For non-Error objects, just throw them
    //         throw submitError;
    //       }
    //     }

    //   } catch (err) {
    //     console.error('Signup submission error:', err);
    //     if (err instanceof Error) {
    //       // Check if the error message contains specific error types
    //       // Error message in outer catch block

    //       // Handle "Missing required signup information" error
    //       if (typeof err.message === 'string' && err.message.includes('Missing required signup information')) {
    //         // Missing signup data error detected
    //         setError('Missing required signup information. Please try again.');
    //         return;
    //       }

    //       // Handle "Failed to fetch" error (API connection issue)
    //       if (typeof err.message === 'string' && err.message.includes('Failed to fetch')) {
    //         console.log('API connection error detected');
    //         setError('Unable to connect to the server. Please try again later.');
    //         return;
    //       }

    //       // Email already registered errors should be caught at the user details page
    //       // This is just a fallback in case something goes wrong
    //       if (typeof err.message === 'string' && err.message.includes('Email already registered')) {
    //         console.log('Unexpected email already registered error in outer catch block - this should have been caught earlier');

    //         // Just log the error and continue with normal flow
    //         console.log('Continuing with normal flow despite email error');

    //         // Set as submitted to prevent further submission attempts
    //         setIsSubmitted(true);
    //         return; // Exit early to prevent further processing
    //       }
    //       // Handle backend error response (empty response)
    //       else if (typeof err.message === 'string' && err.message.includes('Backend error response')) {
    //         console.log('Backend error response detected in outer catch block');

    //         // Generic error handling for backend errors
    //         setError('The server encountered an error. Please try again later.');
    //         setIsSubmitted(true);

    //         // Set debug data for development
    //         setDebugData({
    //           error: 'Backend error response',
    //           message: err.message,
    //           status: 400
    //         });
    //         return; // Exit early to prevent further processing
    //       }
    //       // Email already registered errors should be caught at the user details page
    //       // This is just a fallback in case something goes wrong
    //       else if (typeof err.message === 'string' && err.message.includes('EMAIL_ALREADY_REGISTERED:')) {
    //         console.log('Unexpected EMAIL_ALREADY_REGISTERED error in outer catch block - this should have been caught earlier');

    //         // Just log the error and continue with normal flow
    //         console.log('Continuing with normal flow despite email error');

    //         // Set as submitted to prevent further submission attempts
    //         setIsSubmitted(true);
    //         return; // Exit early to prevent further processing
    //       } else if (typeof err.message === 'string' && err.message.includes('VALIDATION_ERROR:')) {
    //         let errorMessage = '';
    //         try {
    //           errorMessage = err.message.split(':').slice(1).join(':').trim();
    //         } catch (e) {
    //           console.error('Error parsing error message:', e);
    //         }
    //         setError(typeof errorMessage === 'string' && errorMessage ? errorMessage : 'Please check your input data and try again.');
    //       } else if (typeof err.message === 'string' && (err.message.includes('SERVER_ERROR:') || err.message.includes('timed out'))) {
    //         console.log('Server error or timeout detected in outer catch block');

    //         // Show the error message
    //         let errorMessage = '';
    //         if (err.message.includes('SERVER_ERROR:')) {
    //           try {
    //             errorMessage = err.message.split(':').slice(1).join(':').trim();
    //           } catch (e) {
    //             console.error('Error parsing error message:', e);
    //           }
    //         } else {
    //           errorMessage = 'The server is taking too long to respond. Please try again later.';
    //         }
    //         setError(typeof errorMessage === 'string' && errorMessage ? errorMessage : 'The server encountered an error. Please try again later.');
    //         setIsSubmitted(true);
    //       } else {
    //         // Other errors
    //         console.log('Other error detected in outer catch block:', err.message);
    //         setError(err.message);
    //       }
    //     } else {
    //       setError(t("error_generic"));
    //     }
    //   } finally {
    //     setIsSubmitting(false);
    //   }
    // };

    // BYPASS: Disabled submitSignup() call since we're using submitSignupWithBypass instead
    // submitSignup();
  // }, [router, lng]); // BYPASS: Removed isSubmitted and isSubmitting from dependencies to prevent infinite loop

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-white">
      <div className="w-full max-w-xl sm:max-w-2xl md:max-w-3xl p-5 sm:p-6 md:p-8 space-y-4 sm:space-y-6 bg-white border border-gray-200 rounded-[20px] sm:rounded-[30px] md:rounded-[40px] shadow-md">
        <div>
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-between items-center mb-6 sm:mb-8 md:mb-10">
            <div>
              <img
                src="/images/logo_icon.png"
                alt="Lumalife icon"
                className="h-8 sm:h-9 md:h-10 w-auto"
              />
            </div>
            <div className="hidden sm:block">
              <img
                src="/images/logo_full.png"
                alt="Lumalife"
                className="h-8 sm:h-9 md:h-10 w-auto"
              />
            </div>
            <div className="flex items-center">
              <button className="mr-2 sm:mr-4 text-gray-600 text-xl sm:text-2xl">?</button>
              <button className="text-gray-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
                  />
                </svg>
              </button>
            </div>
          </div>

          <div className="text-center max-w-md sm:max-w-xl md:max-w-2xl mx-auto">
            {isInitializing ? (
              // Show loading state while initializing
              <>
                <p className="text-lg sm:text-xl text-gray-800 mb-4 sm:mb-6 md:mb-8 leading-relaxed">
                  {lng === "de" ? "Initialisierung..." : "Initializing..."}
                </p>
                <div className="flex justify-center">
                  <svg className="animate-spin h-8 w-8 text-[#52bcc3]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              </>
            ) : isApprovalPending ? (
              // Show pending approval message for authenticated users who are not approved
              <>
                <p className="text-lg sm:text-xl text-gray-800 mb-4 sm:mb-6 md:mb-8 leading-relaxed">
                  {lng === "de" ? "Dein Account muss noch bestätigt werden" : "Account pending approval"}
                </p>

                <div className="p-3 sm:p-4 mb-4 sm:mb-6 md:mb-8 mx-2 sm:mx-4 bg-blue-500/10 border border-blue-500/30 rounded-md text-blue-700 text-sm">
                  <p className="font-medium">
                    {lng === "de" ? "Dein Account wurde erfolgreich registriert. Wir überprüfen deine Angaben und bestätigen deinen Zugang zu Luma in Kürze. Sobald dein Zugang bestätigt ist, hast du Zugriff auf alle Features." : "Your account has been registered successfully. An administrator will review your account and approve it shortly. You'll be able to access all features once approved."}
                  </p>
                  <p className="mt-2 text-xs text-blue-600">
                    <span className="inline-block mr-2">
                      {isCheckingApproval ? (
                        <svg className="animate-spin h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      ) : (
                        <svg className="h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      )}
                    </span>
                    {lng === "de" ? "Dein Account-Status wird laufend überprüft. Du wirst automatisch zur App weitergeleitet, sobald dein Zugang bestätigt ist." : "We're automatically checking your approval status. You'll be redirected once your account is approved."}
                  </p>
                </div>

                <p className="text-xl sm:text-2xl font-medium text-gray-800 mt-8 sm:mt-12 md:mt-16">
                  {lng === "de" ? "Dein Luma Life Team" : "Your Luma Life Team"}
                </p>

                {/* Logout button */}
                <div className="mt-6 sm:mt-8 md:mt-10">
                  <button
                    onClick={handleLogout}
                    className="py-2 px-4 sm:px-6 bg-[#52bcc3] text-white text-sm sm:text-base rounded-full font-medium hover:bg-[#3da8af] transition-colors"
                  >
                    {t("logout")}
                  </button>
                </div>
              </>
            ) : isSubmitting ? (
              <p className="text-lg sm:text-xl text-gray-800 mb-8 sm:mb-12 md:mb-16 leading-relaxed">
                {t("processing")}...
              </p>
            ) : isSubmitted ? (
              // Show success message even if there was a database error
              <>
                <p className="text-base sm:text-lg md:text-xl text-gray-800 mb-8 sm:mb-12 md:mb-16 leading-relaxed">
                  {t("confirmation_message")}
                </p>

                <p className="text-xl sm:text-2xl font-medium text-gray-800 mt-8 sm:mt-12 md:mt-16">
                  {t("confirmation_team")}
                </p>

                {/* Logout button */}
                <div className="mt-6 sm:mt-8 md:mt-10">
                  <button
                    onClick={handleLogout}
                    className="py-2 px-4 sm:px-6 bg-[#52bcc3] text-white text-sm sm:text-base rounded-full font-medium hover:bg-[#3da8af] transition-colors"
                  >
                    {t("logout")}
                  </button>
                </div>

                {error && typeof error === 'string' && error.includes('database connection error') && (
                  <div className="mt-6 sm:mt-8">
                    <div className="p-3 sm:p-4 mb-4 sm:mb-6 mx-2 sm:mx-4 bg-yellow-500/10 border border-yellow-500/30 rounded-md text-yellow-700 text-xs sm:text-sm">
                      <p>{error}</p>
                    </div>
                  </div>
                )}

                {/* Backend unavailable message removed */}

                {/* No continue button */}
              </>
            ) : error ? (
              <>
                <div className="p-3 sm:p-4 mb-4 sm:mb-6 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 text-red-500 rounded-md text-xs sm:text-sm">
                  <p className="font-medium">{error}</p>
                </div>

                {/* Debug display removed */}
              </>
            ) : (
              <>
                <p className="text-base sm:text-lg md:text-xl text-gray-800 mb-8 sm:mb-12 md:mb-16 leading-relaxed">
                  {t("confirmation_message")}
                </p>

                <p className="text-xl sm:text-2xl font-medium text-gray-800 mt-8 sm:mt-12 md:mt-16">
                  {t("confirmation_team")}
                </p>

                {/* Logout button */}
                <div className="mt-6 sm:mt-8 md:mt-10">
                  <button
                    onClick={handleLogout}
                    className="py-2 px-4 sm:px-6 bg-[#52bcc3] text-white text-sm sm:text-base rounded-full font-medium hover:bg-[#3da8af] transition-colors"
                  >
                    {t("logout")}
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
