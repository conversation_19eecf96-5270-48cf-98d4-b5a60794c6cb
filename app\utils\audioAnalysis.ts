// Audio Analysis Utilities for Voice Logging
// Extracts frequency, amplitude, and silence detection from audio data

export interface AudioAnalysisResult {
  frequencyHz: number;
  amplitudeDb: number;
  audioDataSize: number;
  sampleRate: number;
  channels: number;
  silenceDetected: boolean;
  peakFrequency?: number;
  averageAmplitude?: number;
  rmsAmplitude?: number;
  maxAmplitude?: number;
  peakRMS?: number;
}

/**
 * Analyze audio data to extract frequency and amplitude information
 * @param audioData - Base64 encoded audio data or raw audio buffer
 * @param sampleRate - Sample rate of the audio
 * @param channels - Number of audio channels
 * @returns AudioAnalysisResult with frequency and amplitude data
 */
export function analyzeAudioData(
  audioData: string | ArrayBuffer | Float32Array,
  sampleRate: number = 24000,
  channels: number = 1
): AudioAnalysisResult {
  try {
    let audioBuffer: Float32Array;
    let dataSize: number;

    // Handle empty or invalid input
    if (!audioData ||
        (typeof audioData === 'string' && audioData.length === 0) ||
        (audioData instanceof ArrayBuffer && audioData.byteLength === 0) ||
        (audioData instanceof Float32Array && audioData.length === 0)) {

      return {
        frequencyHz: 0,
        amplitudeDb: -Infinity,
        audioDataSize: 0,
        sampleRate,
        channels,
        silenceDetected: true,
        peakFrequency: 0,
        averageAmplitude: 0,
        rmsAmplitude: 0,
        maxAmplitude: 0,
        peakRMS: 0
      };
    }

    // Convert different input types to Float32Array
    if (typeof audioData === 'string') {
      // Base64 encoded data
      try {
        const binaryString = atob(audioData);
        if (binaryString.length === 0) {
          return {
            frequencyHz: 0,
            amplitudeDb: -Infinity,
            audioDataSize: 0,
            sampleRate,
            channels,
            silenceDetected: true,
            peakFrequency: 0,
            averageAmplitude: 0,
            rmsAmplitude: 0,
            maxAmplitude: 0,
            peakRMS: 0
          };
        }

        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        // Convert bytes to Int16Array then to Float32Array
        const int16Array = new Int16Array(bytes.buffer);
        audioBuffer = new Float32Array(int16Array.length);
        for (let i = 0; i < int16Array.length; i++) {
          audioBuffer[i] = int16Array[i] / 32768.0; // Normalize to [-1, 1]
        }
        dataSize = binaryString.length;
      } catch (decodeError) {
        console.error('Error decoding base64 audio data:', decodeError);
        return {
          frequencyHz: 0,
          amplitudeDb: -Infinity,
          audioDataSize: 0,
          sampleRate,
          channels,
          silenceDetected: true,
          peakFrequency: 0,
          averageAmplitude: 0,
          rmsAmplitude: 0,
          maxAmplitude: 0,
          peakRMS: 0
        };
      }
    } else if (audioData instanceof ArrayBuffer) {
      const int16Array = new Int16Array(audioData);
      audioBuffer = new Float32Array(int16Array.length);
      for (let i = 0; i < int16Array.length; i++) {
        audioBuffer[i] = int16Array[i] / 32768.0;
      }
      dataSize = audioData.byteLength;
    } else {
      // Already Float32Array
      audioBuffer = audioData;
      dataSize = audioData.length * 4; // 4 bytes per float
    }

    // Additional validation for empty buffer
    if (audioBuffer.length === 0) {
      return {
        frequencyHz: 0,
        amplitudeDb: -Infinity,
        audioDataSize: 0,
        sampleRate,
        channels,
        silenceDetected: true,
        peakFrequency: 0,
        averageAmplitude: 0,
        rmsAmplitude: 0,
        maxAmplitude: 0,
        peakRMS: 0
      };
    }

    // Calculate RMS amplitude
    const rmsAmplitude = calculateRMS(audioBuffer);

    // Calculate average amplitude
    const averageAmplitude = calculateAverageAmplitude(audioBuffer);

    // Calculate max amplitude for debugging
    const maxAmplitude = calculateMaxAmplitude(audioBuffer);

    // Convert RMS to dB with proper validation
    let amplitudeDb: number;
    if (rmsAmplitude <= 0 || !Number.isFinite(rmsAmplitude)) {
      amplitudeDb = -Infinity;
    } else {
      amplitudeDb = 20 * Math.log10(rmsAmplitude);
      // Ensure reasonable dB range
      if (!Number.isFinite(amplitudeDb) || amplitudeDb < -120) {
        amplitudeDb = -Infinity;
      }
    }

    // Detect silence with multiple criteria
    const silenceDetected =
      amplitudeDb < -40 ||
      rmsAmplitude < 0.001 ||
      maxAmplitude < 0.001 ||
      dataSize === 0;

    // Calculate dominant frequency (only if not silent)
    let frequencyHz = 0;
    let peakFrequency = 0;

    if (!silenceDetected && audioBuffer.length > 0) {
      frequencyHz = calculateDominantFrequency(audioBuffer, sampleRate);
      peakFrequency = findPeakFrequency(audioBuffer, sampleRate);

      // Validate frequency values
      if (!Number.isFinite(frequencyHz) || frequencyHz < 0) {
        frequencyHz = 0;
      }
      if (!Number.isFinite(peakFrequency) || peakFrequency < 0) {
        peakFrequency = 0;
      }
    }

    // Final validation: if audioDataSize is 0, force silence
    if (dataSize === 0) {
      return {
        frequencyHz: 0,
        amplitudeDb: -Infinity,
        audioDataSize: 0,
        sampleRate,
        channels,
        silenceDetected: true,
        peakFrequency: 0,
        averageAmplitude: 0,
        rmsAmplitude: 0,
        maxAmplitude: 0,
        peakRMS: 0
      };
    }

    return {
      frequencyHz: Math.round(frequencyHz),
      amplitudeDb: Number.isFinite(amplitudeDb) ? Math.round(amplitudeDb * 10) / 10 : -Infinity,
      audioDataSize: dataSize,
      sampleRate,
      channels,
      silenceDetected,
      peakFrequency: Math.round(peakFrequency),
      averageAmplitude: Number.isFinite(averageAmplitude) ? Math.round(averageAmplitude * 1000) / 1000 : 0,
      rmsAmplitude: Number.isFinite(rmsAmplitude) ? Math.round(rmsAmplitude * 1000) / 1000 : 0,
      maxAmplitude: Number.isFinite(maxAmplitude) ? Math.round(maxAmplitude * 1000) / 1000 : 0,
      peakRMS: Number.isFinite(rmsAmplitude) ? Math.round(rmsAmplitude * 1000) / 1000 : 0 // Same as RMS for now
    };
  } catch (error) {
    console.error('Error analyzing audio data:', error);
    
    // Return default values on error
    return {
      frequencyHz: 0,
      amplitudeDb: -Infinity,
      audioDataSize: typeof audioData === 'string' ? audioData.length : 0,
      sampleRate,
      channels,
      silenceDetected: true,
      peakFrequency: 0,
      averageAmplitude: 0,
      rmsAmplitude: 0,
      maxAmplitude: 0,
      peakRMS: 0
    };
  }
}

/**
 * Calculate RMS (Root Mean Square) amplitude
 */
function calculateRMS(audioBuffer: Float32Array): number {
  let sum = 0;
  for (let i = 0; i < audioBuffer.length; i++) {
    sum += audioBuffer[i] * audioBuffer[i];
  }
  return Math.sqrt(sum / audioBuffer.length);
}

/**
 * Calculate average amplitude (absolute values)
 */
function calculateAverageAmplitude(audioBuffer: Float32Array): number {
  let sum = 0;
  for (let i = 0; i < audioBuffer.length; i++) {
    sum += Math.abs(audioBuffer[i]);
  }
  return sum / audioBuffer.length;
}

/**
 * Calculate maximum amplitude (peak value)
 */
function calculateMaxAmplitude(audioBuffer: Float32Array): number {
  let max = 0;
  for (let i = 0; i < audioBuffer.length; i++) {
    const abs = Math.abs(audioBuffer[i]);
    if (abs > max) {
      max = abs;
    }
  }
  return max;
}

/**
 * Calculate dominant frequency using zero-crossing rate and autocorrelation
 */
function calculateDominantFrequency(audioBuffer: Float32Array, sampleRate: number): number {
  try {
    // Use autocorrelation to find the fundamental frequency
    const autocorrelation = autoCorrelate(audioBuffer, sampleRate);
    return autocorrelation;
  } catch (error) {
    console.error('Error calculating dominant frequency:', error);
    return 0;
  }
}

/**
 * Simple autocorrelation-based pitch detection
 */
function autoCorrelate(buffer: Float32Array, sampleRate: number): number {
  const SIZE = buffer.length;
  const MAX_SAMPLES = Math.floor(SIZE / 2);
  let bestOffset = -1;
  let bestCorrelation = 0;
  let rms = 0;
  let foundGoodCorrelation = false;
  const correlations = new Array(MAX_SAMPLES);

  // Calculate RMS
  for (let i = 0; i < SIZE; i++) {
    const val = buffer[i];
    rms += val * val;
  }
  rms = Math.sqrt(rms / SIZE);
  
  // Not enough signal
  if (rms < 0.01) return 0;

  let lastCorrelation = 1;
  for (let offset = 1; offset < MAX_SAMPLES; offset++) {
    let correlation = 0;

    for (let i = 0; i < MAX_SAMPLES; i++) {
      correlation += Math.abs((buffer[i]) - (buffer[i + offset]));
    }
    correlation = 1 - (correlation / MAX_SAMPLES);
    correlations[offset] = correlation;

    if (correlation > 0.9 && correlation > lastCorrelation) {
      foundGoodCorrelation = true;
      if (correlation > bestCorrelation) {
        bestCorrelation = correlation;
        bestOffset = offset;
      }
    } else if (foundGoodCorrelation) {
      const shift = (correlations[bestOffset + 1] - correlations[bestOffset - 1]) / correlations[bestOffset];
      return sampleRate / (bestOffset + (8 * shift));
    }
    lastCorrelation = correlation;
  }
  
  if (bestCorrelation > 0.01) {
    return sampleRate / bestOffset;
  }
  return 0;
}

/**
 * Find peak frequency using simplified FFT analysis
 */
function findPeakFrequency(audioBuffer: Float32Array, sampleRate: number): number {
  try {
    // Simple frequency analysis using zero-crossing rate
    let crossings = 0;
    for (let i = 1; i < audioBuffer.length; i++) {
      if ((audioBuffer[i] >= 0) !== (audioBuffer[i - 1] >= 0)) {
        crossings++;
      }
    }
    
    // Estimate frequency from zero crossings
    const duration = audioBuffer.length / sampleRate;
    const frequency = (crossings / 2) / duration;
    
    return Math.round(frequency);
  } catch (error) {
    console.error('Error finding peak frequency:', error);
    return 0;
  }
}

/**
 * Analyze microphone input audio for real-time monitoring
 */
export function analyzeMicrophoneAudio(
  inputBuffer: Float32Array,
  sampleRate: number = 24000
): AudioAnalysisResult {
  return analyzeAudioData(inputBuffer, sampleRate, 1);
}

/**
 * Analyze assistant audio output for playback monitoring
 */
export function analyzeAssistantAudio(
  base64AudioData: string,
  sampleRate: number = 24000
): AudioAnalysisResult {
  return analyzeAudioData(base64AudioData, sampleRate, 1);
}
