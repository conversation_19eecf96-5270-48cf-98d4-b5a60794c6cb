"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import { getTranslation } from "../i18n/translations";
import { resetPassword } from "../services/api";

export default function ResetPasswordPage({ lng }: { lng: string }) {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [token, setToken] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [passwordStrength, setPasswordStrength] = useState(0);

  const router = useRouter();
  const searchParams = useSearchParams();

  // Helper function to get translation
  const t = (key: string) => getTranslation(key, lng);

  // Extract token from URL on component mount
  useEffect(() => {
    const tokenParam = searchParams.get('token');
    if (!tokenParam) {
      setError(t("invalid_reset_token") || "Invalid or missing reset token");
    } else {
      setToken(tokenParam);
    }
  }, [searchParams, t]);

  // Function to check password strength - simplified to only check minimum length
  const checkPasswordStrength = (password: string) => {
    let strength = 0;

    // Check length - minimum 8 characters
    if (password.length >= 8) {
      strength = 4; // Set to maximum strength if minimum length is met
    } else {
      strength = Math.floor((password.length / 8) * 4);
    }

    setPasswordStrength(strength);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setNewPassword(password);
    checkPasswordStrength(password);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords
    if (!newPassword.trim()) {
      setError(t("password_required") || "Password is required");
      return;
    }

    if (newPassword.length < 8) {
      setError(t("password_too_short") || "Password must be at least 8 characters long");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError(t("passwords_do_not_match") || "Passwords do not match");
      return;
    }

    if (!token) {
      setError(t("invalid_reset_token") || "Invalid or missing reset token");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Call the API to reset the password
      await resetPassword(token, newPassword);
      setSuccess(true);

      // Redirect to login page after 3 seconds
      setTimeout(() => {
        router.push(`/${lng}/login`);
      }, 3000);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message || t("error_generic") || "An error occurred. Please try again.");
      } else {
        setError(t("error_generic") || "An error occurred. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-gradient-to-b from-white to-gray-50">
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md p-6 sm:p-8 md:p-10 space-y-6 sm:space-y-8 bg-white border border-gray-200 rounded-2xl sm:rounded-3xl shadow-lg">
        <div className="text-center">
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-10 sm:h-12 md:h-14 w-auto rounded-xl overflow-hidden"
            />
          </div>

          <h2 className="text-xl sm:text-2xl font-semibold text-gray-800 mb-4 sm:mb-6">
            {lng === "de" ? "Neues Passwort festlegen" : "Set New Password"}
          </h2>

          {!success ? (
            <>
              <p className="mb-6 sm:mb-8 text-gray-700 px-2 sm:px-4 text-sm sm:text-base">
                {lng === "de"
                  ? "Bitte geben Sie Ihr neues Passwort ein."
                  : "Please enter your new password."}
              </p>

              {error && (
                <div className="p-3 sm:p-4 mb-4 sm:mb-5 bg-red-50 border border-red-200 rounded-lg text-red-600 text-xs sm:text-sm shadow-sm">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {error}
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-5 sm:space-y-6">
                <div className="relative">
                  <label className="block text-left text-sm font-medium text-gray-700 mb-1">
                    {lng === "de" ? "Neues Passwort" : "New Password"}
                  </label>
                  <input
                    type="password"
                    value={newPassword}
                    onChange={handlePasswordChange}
                    placeholder={lng === "de" ? "Neues Passwort eingeben" : "Enter new password"}
                    className="w-full px-4 py-3 border border-gray-300 rounded-full text-sm sm:text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#52bcc3] focus:border-transparent transition-all"
                    required
                  />

                  {/* Password strength indicator */}
                  <div className="mt-2 h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className={`h-full transition-all duration-300 ${
                        passwordStrength === 0 ? 'w-0' :
                        passwordStrength === 1 ? 'w-1/4 bg-red-500' :
                        passwordStrength === 2 ? 'w-2/4 bg-orange-500' :
                        passwordStrength === 3 ? 'w-3/4 bg-yellow-500' :
                        'w-full bg-green-500'
                      }`}
                    ></div>
                  </div>
                  <div className="flex justify-between mt-1.5">
                    <p className="text-xs text-gray-500">
                      {lng === "de" ? "Mindestens 8 Zeichen" : "Minimum 8 characters"}
                    </p>
                    <p className="text-xs font-medium"
                      style={{
                        color: passwordStrength === 0 ? '#9CA3AF' :
                               passwordStrength === 1 ? '#EF4444' :
                               passwordStrength === 2 ? '#F97316' :
                               passwordStrength === 3 ? '#EAB308' :
                               '#22C55E'
                      }}
                    >
                      {passwordStrength === 0 ? (lng === "de" ? "Keine Eingabe" : "No input") :
                       passwordStrength === 1 ? (lng === "de" ? "Schwach" : "Weak") :
                       passwordStrength === 2 ? (lng === "de" ? "Mittel" : "Medium") :
                       passwordStrength === 3 ? (lng === "de" ? "Gut" : "Good") :
                       (lng === "de" ? "Stark" : "Strong")}
                    </p>
                  </div>
                </div>

                <div className="relative">
                  <label className="block text-left text-sm font-medium text-gray-700 mb-1">
                    {lng === "de" ? "Passwort bestätigen" : "Confirm Password"}
                  </label>
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder={lng === "de" ? "Passwort bestätigen" : "Confirm your password"}
                    className={`w-full px-4 py-3 border rounded-full text-sm sm:text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#52bcc3] focus:border-transparent transition-all ${
                      confirmPassword && newPassword && confirmPassword !== newPassword
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-300'
                    }`}
                    required
                  />
                  {confirmPassword && newPassword && confirmPassword !== newPassword && (
                    <p className="mt-1 text-xs text-red-500 text-left">
                      {lng === "de" ? "Passwörter stimmen nicht überein" : "Passwords don't match"}
                    </p>
                  )}
                </div>

                <div className="pt-2 sm:pt-3">
                  <button
                    type="submit"
                    disabled={isSubmitting || !token}
                    className={`w-full py-3 sm:py-3.5 px-4 sm:px-6 text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 ${
                      isSubmitting || !token
                        ? 'bg-gray-400 cursor-not-allowed opacity-70'
                        : 'bg-[#52bcc3] hover:bg-[#3da8af] hover:shadow-md'
                    }`}
                  >
                    {isSubmitting
                      ? (lng === "de" ? "Wird gespeichert..." : "Saving...")
                      : (lng === "de" ? "Passwort speichern" : "Save Password")}
                  </button>
                </div>
              </form>
            </>
          ) : (
            <div className="bg-green-50 border border-green-200 rounded-lg p-5 text-green-700 shadow-sm">
              <div className="flex items-center justify-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="mb-4 font-medium text-base">
                {lng === "de"
                  ? "Ihr Passwort wurde erfolgreich zurückgesetzt."
                  : "Your password has been successfully reset."}
              </p>
              <p className="text-sm">
                {lng === "de"
                  ? "Sie werden zur Anmeldeseite weitergeleitet..."
                  : "You are being redirected to the login page..."}
              </p>
            </div>
          )}

          <div className="text-center text-xs sm:text-sm text-gray-700 mt-5 sm:mt-6">
            <Link href={`/${lng}/login`} className="text-[#52bcc3] hover:underline font-medium">
              {lng === "de" ? "Zurück zur Anmeldung" : "Back to Login"}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
