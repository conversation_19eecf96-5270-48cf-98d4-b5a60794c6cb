import { NextRequest, NextResponse } from 'next/server';

// API route to handle assistant status requests
export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Missing authorization token' }, { status: 401 });
    }

    // Forward the request to the backend API
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
    
    try {
      const backendResponse = await fetch(`${backendUrl}/assistants/status`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (backendResponse.ok) {
        const data = await backendResponse.json();
        console.log('Assistant status check successful:', data);
        return NextResponse.json(data);
      } else {
        console.log(`Backend responded with status ${backendResponse.status}`);
        
        // If backend doesn't have the endpoint yet, return a default response
        if (backendResponse.status === 404) {
          // Default to 'complete' status for now until backend implements the endpoint
          return NextResponse.json({ status: 'complete' });
        }
        
        return NextResponse.json({ error: 'Backend status check failed' }, { status: backendResponse.status });
      }
    } catch (backendError) {
      console.error('Error forwarding to backend:', backendError);
      
      // Return a default response if backend is not available
      return NextResponse.json({ status: 'complete' });
    }
  } catch (error) {
    console.error('Error in assistant status endpoint:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
