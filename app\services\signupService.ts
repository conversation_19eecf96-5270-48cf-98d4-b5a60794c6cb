// Signup Service to manage the multi-step signup process

import { signup, SignupRequest, UserDetails } from './api';

// Store signup data during the process
class SignupStore {
  private static instance: SignupStore;
  private _phoneData: { countryCode: string; phoneNumber: string } | null = null;
  private _userDetails: Partial<UserDetails> | null = null;
  private _email: string | null = null;
  private _password: string | null = null;
  private _organizationName: string | null = null;
  private _avatarGender: string | null = null;
  private _language: string | null = null;
  private _isSubmitting: boolean = false;
  private _submissionPromise: Promise<any> | null = null;

  private constructor() {}

  public static getInstance(): SignupStore {
    if (!SignupStore.instance) {
      SignupStore.instance = new SignupStore();
    }
    return SignupStore.instance;
  }

  // Phone verification step
  public setPhoneData(countryCode: string, phoneNumber: string): void {
    // countryCode should be a string of letters (like "US", "UK", etc.)
    // phoneNumber should include the prefix (like "+1", "+44", etc.)
    this._phoneData = { countryCode, phoneNumber };
    // Store in localStorage as a backup
    try {
      localStorage.setItem('signupPhoneData', JSON.stringify(this._phoneData));
    } catch (e) {
      console.error('Failed to store phone data in localStorage:', e);
    }
  }

  public getPhoneData(): { countryCode: string; phoneNumber: string } | null {
    // If data is missing from memory, try to retrieve from localStorage
    if (!this._phoneData) {
      try {
        const storedData = localStorage.getItem('signupPhoneData');
        if (storedData) {
          this._phoneData = JSON.parse(storedData);
        }
      } catch (e) {
        console.error('Failed to retrieve phone data from localStorage:', e);
      }
    }
    return this._phoneData;
  }

  // User details step
  public setUserDetails(
    firstName: string,
    lastName: string,
    dob: string,
    email: string,
    password: string
  ): void {
    this._userDetails = {
      first_name: firstName,
      last_name: lastName,
      birthdate: dob,
    };
    this._email = email;
    this._password = password;

    // Store in localStorage as a backup
    try {
      localStorage.setItem('signupUserDetails', JSON.stringify(this._userDetails));
      localStorage.setItem('signupEmail', email);
      localStorage.setItem('signupPassword', password); // Note: storing password in localStorage is not secure for production
    } catch (e) {
      console.error('Failed to store user details in localStorage:', e);
    }
  }

  public getUserDetails(): Partial<UserDetails> | null {
    // If data is missing from memory, try to retrieve from localStorage
    if (!this._userDetails) {
      try {
        const storedData = localStorage.getItem('signupUserDetails');
        if (storedData) {
          this._userDetails = JSON.parse(storedData);
          console.log('Retrieved user details from localStorage:', this._userDetails);
        }
      } catch (e) {
        console.error('Failed to retrieve user details from localStorage:', e);
      }
    }
    return this._userDetails;
  }

  public getEmail(): string | null {
    // If data is missing from memory, try to retrieve from localStorage
    if (!this._email) {
      try {
        const storedEmail = localStorage.getItem('signupEmail');
        if (storedEmail) {
          this._email = storedEmail;
          console.log('Retrieved email from localStorage:', this._email);
        }
      } catch (e) {
        console.error('Failed to retrieve email from localStorage:', e);
      }
    }
    return this._email;
  }

  public getPassword(): string | null {
    // If data is missing from memory, try to retrieve from localStorage
    if (!this._password) {
      try {
        const storedPassword = localStorage.getItem('signupPassword');
        if (storedPassword) {
          this._password = storedPassword;
          console.log('Retrieved password from localStorage');
        }
      } catch (e) {
        console.error('Failed to retrieve password from localStorage:', e);
      }
    }
    return this._password;
  }

  // Organization step
  public setOrganizationName(orgName: string): void {
    this._organizationName = orgName;

    // Log to verify data is being stored
    console.log('Organization name stored:', this._organizationName);

    // Store in localStorage as a backup
    try {
      localStorage.setItem('signupOrgName', orgName);
    } catch (e) {
      console.error('Failed to store organization name in localStorage:', e);
    }
  }

  public getOrganizationName(): string | null {
    // If data is missing from memory, try to retrieve from localStorage
    if (!this._organizationName) {
      try {
        const storedOrgName = localStorage.getItem('signupOrgName');
        if (storedOrgName) {
          this._organizationName = storedOrgName;
          console.log('Retrieved organization name from localStorage:', this._organizationName);
        }
      } catch (e) {
        console.error('Failed to retrieve organization name from localStorage:', e);
      }
    }
    return this._organizationName;
  }

  // Avatar gender step
  public setAvatarGender(gender: string): void {
    this._avatarGender = gender;

    // Log to verify data is being stored
    console.log('Avatar gender stored:', this._avatarGender);

    // Store in localStorage as a backup
    try {
      localStorage.setItem('signupAvatarGender', gender);
    } catch (e) {
      console.error('Failed to store avatar gender in localStorage:', e);
    }
  }

  public getAvatarGender(): string | null {
    // If data is missing from memory, try to retrieve from localStorage
    if (!this._avatarGender) {
      try {
        const storedGender = localStorage.getItem('signupAvatarGender');
        if (storedGender) {
          this._avatarGender = storedGender;
          console.log('Retrieved avatar gender from localStorage:', this._avatarGender);
        }
      } catch (e) {
        console.error('Failed to retrieve avatar gender from localStorage:', e);
      }
    }
    return this._avatarGender;
  }

  // Language preference
  public setLanguage(language: string): void {
    this._language = language;

    // Log to verify data is being stored
    console.log('Language preference stored:', this._language);

    // Store in localStorage as a backup
    try {
      localStorage.setItem('signupLanguage', language);
    } catch (e) {
      console.error('Failed to store language preference in localStorage:', e);
    }
  }

  public getLanguage(): string | null {
    // If data is missing from memory, try to retrieve from localStorage
    if (!this._language) {
      try {
        const storedLanguage = localStorage.getItem('signupLanguage');
        if (storedLanguage) {
          this._language = storedLanguage;
          console.log('Retrieved language preference from localStorage:', this._language);
        }
      } catch (e) {
        console.error('Failed to retrieve language preference from localStorage:', e);
      }
    }
    return this._language;
  }

  // Final submission
  public async submitSignup(): Promise<any> {
    // Check if a submission is already in progress
    if (this._isSubmitting && this._submissionPromise) {
      console.log('Signup already in progress, returning existing promise');
      return this._submissionPromise;
    }

    // BYPASS: Modified validation to only check essential fields since we're bypassing phone and birthdate
    // if (!this._phoneData || !this._userDetails || !this._email || !this._password) {
    if (!this._email || !this._password) {
      throw new Error('Missing required signup information');
    }

    // Set submission flag
    this._isSubmitting = true;

    // BYPASS: Comment out birthdate validation and use dummy date
    // Format birthdate to ISO string (YYYY-MM-DD)
    // let birthdate = this._userDetails.birthdate!;

    // Check if the date is in DD-MM-YYYY format (what the user inputs)
    // const ddmmyyyyRegex = /^(\d{2})-(\d{2})-(\d{4})$/;
    // const ddmmyyyyMatch = birthdate.match(ddmmyyyyRegex);

    // if (ddmmyyyyMatch) {
    //   // Convert from DD-MM-YYYY to YYYY-MM-DD
    //   const day = ddmmyyyyMatch[1];
    //   const month = ddmmyyyyMatch[2];
    //   const year = ddmmyyyyMatch[3];
    //   birthdate = `${year}-${month}-${day}`;
    // }
    // // If birthdate is already in YYYY-MM-DD format, use it as is
    // else if (!/^\d{4}-\d{2}-\d{2}$/.test(birthdate)) {
    //   try {
    //     const date = new Date(birthdate);
    //     if (!isNaN(date.getTime())) {
    //       birthdate = date.toISOString().split('T')[0];
    //       console.log('Parsed date to YYYY-MM-DD format:', birthdate);
    //     } else {
    //       throw new Error('Invalid birthdate format. Please use DD-MM-YYYY format.');
    //     }
    //   } catch (e) {
    //     throw new Error('Invalid birthdate format. Please use DD-MM-YYYY format.');
    //   }
    // }

    // BYPASS: Use dummy birthdate since we're not collecting this data
    const birthdate = null;

    // BYPASS: Use dummy/fallback values for bypassed fields
    // Ensure country_code is a string of letters (like "US") and not a number
    // The backend expects country_code to be in letters format
    const userDetails: UserDetails = {
      first_name: this._userDetails?.first_name || "User",
      last_name: this._userDetails?.last_name || "",
      country_code: this._phoneData?.countryCode || "US", // Dummy country code
      mobile_number: this._phoneData?.phoneNumber || "+1", // Dummy phone number
      avatar_gender: this._avatarGender || 'female', // Use avatar gender if available, otherwise default to female
      birthdate: birthdate,
    };

    // Add organization if provided
    if (this._organizationName) {
      userDetails.org_name = this._organizationName;
    }

    // Add language preference if provided
    if (this._language) {
      userDetails.language = this._language;
    } else {
      // Default to 'en' if no language is specified
      userDetails.language = 'en';
    }

    const signupData: SignupRequest = {
      email: this._email,
      password: this._password,
      details: userDetails,
    };

    // Create and store the submission promise
    this._submissionPromise = this.performSignup(signupData);

    try {
      const result = await this._submissionPromise;
      return result;
    } finally {
      // Reset submission state
      this._isSubmitting = false;
      this._submissionPromise = null;
    }
  }

  private async performSignup(signupData: SignupRequest): Promise<any> {
    console.log('Performing actual signup API call for email:', signupData.email);
    return await signup(signupData);
  }

  // Reset store after signup or when needed
  public reset(): void {
    this._phoneData = null;
    this._userDetails = null;
    this._email = null;
    this._password = null;
    this._organizationName = null;
    this._avatarGender = null;
    this._language = null;
    this._isSubmitting = false;
    this._submissionPromise = null;

    // Clear localStorage as well
    try {
      localStorage.removeItem('signupPhoneData');
      localStorage.removeItem('signupUserDetails');
      localStorage.removeItem('signupEmail');
      localStorage.removeItem('signupPassword');
      localStorage.removeItem('signupOrgName');
      localStorage.removeItem('signupAvatarGender');
      localStorage.removeItem('signupLanguage');
    } catch (e) {
      console.error('Failed to clear signup data from localStorage:', e);
    }
  }
}

export const signupService = SignupStore.getInstance();

// Make signupService globally accessible for token recovery
if (typeof window !== 'undefined') {
  (window as any).signupService = signupService;
}
