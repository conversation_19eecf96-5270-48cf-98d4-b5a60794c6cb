"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { getTranslation } from "../i18n/translations";
import { useAuth } from "../context/ApiAuthContext";
import { useBrowserCompatibility } from "../context/BrowserCompatibilityContext";
import AssistantChat, { assistantHand<PERSON> } from "./AssistantCore";
import DynamicAvatar from "./AssistantAvatar";
import { AgentRoleEnum } from "../types/enums";
import { STORAGE_KEYS, AUDIO_CONFIG, AGENT_ROLES } from "../config/appConfig";
import WebSocketControls, { webSocketUtils } from "./AssistantControls";
import * as audioUtils from '../utils/audioStreamingStatic';
import { requestWakeLock, releaseWakeLock } from '../utils/wakeLock';

// Component for feedback thank you screen
export default function FeedbackThankYouScreen({
  lng,
  avatarGender,
  onComplete
}: {
  lng: string;
  avatarGender: "male" | "female";
  onComplete: () => void;
}) {
  // Get user data for language preference
  const { user } = useAuth();
  // Use browser compatibility context instead of local state
  const { isBrowserCompatible, hasMicrophonePermission, error, clearError } = useBrowserCompatibility();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [audioLevel, setAudioLevel] = useState<number>(0);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const assistantChatRef = useRef<any>(null);
  const isUnmountingRef = useRef<boolean>(false); // Ref to track if component is unmounting

  // Handle Lottie reference
  const handleLottieRef = useCallback((instance: any) => {
    console.log("Lottie instance received in FeedbackThankYouScreen:", instance);
    // We don't need to store the instance anymore
  }, []);

  // Monitor assistant speaking state to update avatar
  useEffect(() => {
    if (!isConnected) {
      setIsSpeaking(false);
      return;
    }

    const monitorAssistantSpeaking = async () => {
      try {
        const audioUtils = await import('../utils/audioStreamingStatic');
        const isCurrentlySpeaking = audioUtils.getAssistantSpeakingState();
        setIsSpeaking(isCurrentlySpeaking);
      } catch (err) {
        console.error('Error monitoring assistant speaking state:', err);
      }
    };

    // Poll for speaking state changes
    const interval = setInterval(monitorAssistantSpeaking, 500);

    // Initial check
    monitorAssistantSpeaking();

    return () => clearInterval(interval);
  }, [isConnected]);

  // Use the t function from our static translations
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Effect to handle audio level simulation when recording - optimized to reduce CPU usage
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRecording && isConnected) { // Only simulate when recording AND connected
      // Simulate audio levels when recording - reduced frequency to 300ms for better performance
      interval = setInterval(() => {
        // Generate random audio level between 0.3 and 0.8
        const randomLevel = 0.3 + Math.random() * 0.5;
        setAudioLevel(randomLevel);
      }, 300); // Increased from 200ms to 300ms to reduce CPU usage
    } else {
      // Reset audio level when not recording
      setAudioLevel(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, isConnected]); // Added isConnected dependency

  // Effect to update connection status
  useEffect(() => {
    const updateConnectionStatus = () => {
      const indicator = document.getElementById('connection-indicator');
      const status = document.getElementById('connection-status');

      if (!indicator || !status) return;

      // Check if window.assistantSocket exists and get its state
      const socket = (window as any).assistantSocket;

      if (socket && socket.readyState === WebSocket.OPEN) {
        // Connected
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-green-500';
        indicator.innerHTML = '';
        status.textContent = t("connected") || "Connected";
        status.className = 'font-medium text-green-700';
        console.log('FeedbackThankYouScreen: WebSocket is OPEN, setting isConnected to true');
        setIsConnected(true);
        // Clear any WebSocket-related errors
        if (error && error.includes("communication")) {
          clearError();
        }
      } else if (socket && socket.readyState === WebSocket.CONNECTING) {
        // Connecting
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-yellow-500 relative';
        indicator.innerHTML = '<span class="absolute inset-0 rounded-full bg-yellow-500 animate-ping opacity-75"></span>';
        status.textContent = t("connecting") || "Connecting...";
        status.className = 'font-medium text-yellow-700';
        console.log('FeedbackThankYouScreen: WebSocket is CONNECTING, setting isConnected to false');
        setIsConnected(false);
      } else {
        // Disconnected or error - show "Please wait..." instead
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-yellow-500';
        indicator.innerHTML = '';
        status.textContent = "Please wait...";
        status.className = 'font-medium text-yellow-700';
        setIsConnected(false);
      }
    };

    // Update immediately and periodically
    updateConnectionStatus();
    const interval = setInterval(updateConnectionStatus, 1000);

    // Update when window gets focus
    window.addEventListener('focus', updateConnectionStatus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', updateConnectionStatus);
    };
  }, []);

  // Track connection state changes
  useEffect(() => {
    // Connection state tracking for production
  }, [isConnected]);

  // Wake lock effect - prevent screen auto-lock on mobile/tablet devices
  useEffect(() => {
    let wakeLockRequested = false;

    const handleWakeLock = async () => {
      // Request wake lock when connected to prevent screen auto-lock during feedback conversation
      if (isConnected && !isPaused) {
        if (!wakeLockRequested) {
          const success = await requestWakeLock('FeedbackAssistant');
          if (success) {
            wakeLockRequested = true;
            console.log('FeedbackAssistant: Wake lock acquired to prevent screen auto-lock during feedback');
          }
        }
      } else {
        // Release wake lock when disconnected or paused
        if (wakeLockRequested) {
          await releaseWakeLock('FeedbackAssistant');
          wakeLockRequested = false;
          console.log('FeedbackAssistant: Wake lock released');
        }
      }
    };

    handleWakeLock();

    // Cleanup function to release wake lock when component unmounts
    return () => {
      if (wakeLockRequested) {
        releaseWakeLock('FeedbackAssistant');
        console.log('FeedbackAssistant: Wake lock released on component unmount');
      }
    };
  }, [isConnected, isPaused]);

  // Initialize the chat interface
  useEffect(() => {
    // Skip WebSocket initialization if browser is not compatible
    if (!isBrowserCompatible) {
      return;
    }

    // First, ensure any existing connections are closed
    if ((window as any).assistantSocket) {
      try {
        console.log('FeedbackThankYouScreen: Closing existing global WebSocket before creating new one');
        (window as any).assistantSocket.close(1000, 'Creating new connection');
        (window as any).assistantSocket = null;
      } catch (err) {
        console.error('Error closing existing global WebSocket:', err);
      }
    }

    // Initialize the WebSocket connection with minimal delay
    // Using optimized delay settings for faster performance
    setTimeout(() => {
      // Check if component is unmounting before attempting to connect
      if (isUnmountingRef.current) {
        // Component is unmounting, skipping WebSocket connection
        return;
      }

      if (assistantChatRef.current) {
        // Starting new WebSocket connection for Feedback
        assistantChatRef.current.startConnection()
          .then(() => {
            // Check again if component is unmounting
            if (isUnmountingRef.current) {
              // Component unmounted during connection, closing WebSocket
              assistantChatRef.current?.endConversation();
              return;
            }
            // Feedback WebSocket connection started successfully
          })
          .catch((err: Error) => {
            // Check if component is unmounting before attempting to reconnect
            if (isUnmountingRef.current) {
              console.log('FeedbackThankYouScreen: Component is unmounting, skipping WebSocket reconnection');
              return;
            }

            console.error('FeedbackThankYouScreen: Failed to start Feedback WebSocket connection:', err);
          });
      }
    }, 100); // Reduced from 300ms to 100ms for faster initialization

    // Clean up WebSocket connection when component unmounts
    return () => {
      // Set the unmounting flag to prevent new connections
      isUnmountingRef.current = true;
      console.log('FeedbackThankYouScreen: Component is unmounting, setting unmounting flag');

      // Send voice logs before cleanup
      try {
        import('../utils/voiceLogging').then(({ sendVoiceLogsBatch }) => {
          sendVoiceLogsBatch('component_unmount').catch(err => {
            console.log('Error sending voice logs during FeedbackAssistant unmount:', err);
          });
        }).catch(err => {
          console.log('Error importing voice logging during FeedbackAssistant unmount:', err);
        });
      } catch (err) {
        console.log('Error with voice logging during FeedbackAssistant unmount:', err);
      }

      // Set the unmounting flag in audioUtils to prevent new operations
      audioUtils.setUnmountingFlag(true);

      if (assistantChatRef.current) {
        console.log('FeedbackThankYouScreen: Closing WebSocket connection on unmount');
        assistantChatRef.current.endConversation();
      }

      // Also close any global socket that might be lingering
      if ((window as any).assistantSocket) {
        try {
          console.log('FeedbackThankYouScreen: Closing global WebSocket on unmount');
          (window as any).assistantSocket.close(1000, 'Component unmounted');
          (window as any).assistantSocket = null;
        } catch (err) {
          console.error('Error closing global WebSocket:', err);
        }
      }
    };
  }, []);

  // Handle submit feedback button click
  const handleSubmitFeedback = useCallback(async () => {
    try {
      setIsSubmitting(true);
      // Submit feedback button clicked

      // Send voice logs before finishing feedback
      try {
        import('../utils/voiceLogging').then(({ endVoiceLoggingSession }) => {
          endVoiceLoggingSession('feedback_complete').catch(err => {
            console.log('Error ending voice logging session in FeedbackAssistant:', err);
          });
        }).catch(err => {
          console.log('Error importing voice logging during feedback completion:', err);
        });
      } catch (err) {
        console.log('Error with voice logging during feedback completion:', err);
      }

      // Set the unmounting flag to prevent new connections
      isUnmountingRef.current = true;
      // Setting unmounting flag before navigation

      // Set the unmounting flag in audioUtils to prevent new operations
      audioUtils.setUnmountingFlag(true);

      // Close WebSocket connection
      if (assistantChatRef.current) {
        // Closing WebSocket connection before navigation
        assistantChatRef.current.endConversation();
      }

      // Call onComplete to move to the final thank you screen
      // The stage update to 7 will happen in the parent component
      onComplete();

    } catch (error) {
      console.error('Error submitting feedback:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [onComplete]);

  return (
    <div className="text-center max-w-xs sm:max-w-sm md:max-w-md mx-auto px-2 sm:px-0">


      {/* No error handling needed */}

      <div className="flex flex-col justify-center items-center">
        {/* Avatar display */}
        <div className="flex justify-center items-center mb-3 sm:mb-4 relative w-full">
          <div className="relative mx-auto w-40 h-40 sm:w-48 sm:h-48 md:w-56 md:h-56 lg:w-64 lg:h-64">
            {/* Avatar component */}
            <DynamicAvatar
              gender={avatarGender}
              isActive={isConnected}
              isSpeaking={isSpeaking}
              audioLevel={audioLevel}
              onLottieRef={handleLottieRef}
            />
          </div>
        </div>

        {/* WebSocket controls component */}
        <WebSocketControls
          lng={lng}
          isRecording={isRecording}
          isConnected={isConnected}
          isPaused={isPaused}
          onMicClick={() => {
            // Use the reusable handler function from AssistantCore
            assistantHandlers.handleMicClick(
              assistantChatRef,
              isRecording,
              isUnmountingRef,
              'FeedbackThankYouScreen',
              setIsRecording
            );
          }}
          onStopClick={() => {
            // Use the reusable handler function from AssistantCore
            assistantHandlers.handleStopClick(
              assistantChatRef,
              isPaused,
              isUnmountingRef,
              'FeedbackThankYouScreen',
              setIsPaused
            );
          }}
          onReconnectClick={() => {
            // Use the reusable handler function from AssistantCore
            assistantHandlers.handleReconnectClick(
              assistantChatRef,
              isPaused,
              isUnmountingRef,
              'FeedbackThankYouScreen',
              setIsPaused
            );
          }}
        />

        {/* AssistantChat component to handle WebSocket connection */}
        <div className="h-0 overflow-hidden">
          <AssistantChat
            ref={(ref) => {
              assistantChatRef.current = ref;
              // Check if the ref has the isPaused property and update state accordingly
              if (ref && 'isPaused' in ref) {
                const newPausedState = ref.isPaused;
                // Only update if the state has changed to avoid unnecessary renders
                if (isPaused !== newPausedState) {
                  console.log('FeedbackThankYouScreen: Syncing isPaused state with AssistantChat component:', newPausedState);
                  setIsPaused(newPausedState);
                }
              }
            }}
            userToken={localStorage.getItem(STORAGE_KEYS.TOKEN) || ''}
            avatarGender={avatarGender}
            agentRole={AgentRoleEnum.FA}
            language={user?.details?.language || lng}
            onConversationComplete={() => {
              // Conversation completed
            }}
            onRecordingStateChange={(recording) => {
              setIsRecording(recording);
            }}
            onConnectionStateChange={(connected) => {
              setIsConnected(connected);

              // If connection is lost, reset paused state
              if (!connected) {
                setIsPaused(false);
              }
            }}
          />
        </div>
      </div>

      <div className="mt-4 sm:mt-6">
        <button
          onClick={handleSubmitFeedback}
          disabled={isSubmitting}
          className={`min-w-[140px] sm:min-w-[160px] md:min-w-[180px] py-2 sm:py-2.5 px-4 sm:px-6 ${
            isSubmitting
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer"
          } text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-3 w-3 sm:h-4 sm:w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t("processing") || "Processing..."}
            </span>
          ) : (
            t("submit_feedback") || "Submit Feedback"
          )}
        </button>
      </div>
    </div>
  );
}
