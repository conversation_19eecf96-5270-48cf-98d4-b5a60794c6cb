<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #fff;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .nav-links {
            display: flex;
            gap: 20px;
        }
        .nav-links a {
            text-decoration: none;
            color: #333;
        }
        .main-content {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            margin-top: 0;
        }
        footer {
            margin-top: 20px;
            text-align: center;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">Chatbot App</div>
            <div class="nav-links">
                <a href="#">Home</a>
                <a href="#">Features</a>
                <a href="#">About</a>
            </div>
            <div class="auth-links">
                <a href="#">Sign In</a>
                <a href="#">Sign Up</a>
            </div>
        </nav>
    </header>

    <div class="container">
        <div class="main-content">
            <h1>Welcome to Chatbot App</h1>
            <p>A modern application with authentication and more features coming soon.</p>

            <div style="margin-top: 30px; padding: 20px; background-color: #f9f9f9; border-radius: 8px;">
                <h2>Getting Started</h2>
                <p>Sign in or create an account to get started with our application.</p>
                <p style="font-size: 0.9rem; color: #666;">
                    For demo purposes, you can sign in with any email that contains "user" (e.g., <EMAIL>) and any password.
                </p>
            </div>
        </div>

        <footer>
            <p>© 2024 Chatbot App. All rights reserved.</p>
        </footer>
    </div>

    <script>
        // Measure page load time
        window.addEventListener('load', function() {
            const loadTime = performance.now();

            // Display load time on the page
            const timeDisplay = document.createElement('div');
            timeDisplay.style.position = 'fixed';
            timeDisplay.style.bottom = '10px';
            timeDisplay.style.right = '10px';
            timeDisplay.style.padding = '5px 10px';
            timeDisplay.style.backgroundColor = '#333';
            timeDisplay.style.color = '#fff';
            timeDisplay.style.borderRadius = '4px';
            timeDisplay.textContent = `Load time: ${loadTime.toFixed(2)} ms`;
            document.body.appendChild(timeDisplay);
        });
    </script>
</body>
</html>
