// This file can't directly import npm packages since it runs in an AudioWorklet
// You'll need to pass VAD configuration from your main code

class AudioProcessor extends AudioWorkletProcessor {
    constructor() {
        super();
        this.bufferSize = 4096;
        this.buffer = new Float32Array(this.bufferSize);
        this.bufferIndex = 0;
        this.silenceFrames = 0;
        this.maxSilenceFrames = 30;
        this.isStreaming = false;
        
        // Enhanced VAD configuration
        this.vadContext = {
            bufferSize: 512,
            voiceTrigger: 0.75,
            voiceStop: 0.5,
            smoothingTimeConstant: 0.99,
            history: [],
            energyThreshold: 0.04,   // Increased threshold to reduce background noise sensitivity
            spectralFlatness: 0,     // For frequency domain analysis
            zeroCrossingRate: 0,     // For time domain analysis
            consecutiveVoiceFrames: 0,
            minVoiceFrames: 5        // Require more consecutive voice frames to reduce false positives
        };
        
        // Listen for VAD configuration from main thread
        this.port.onmessage = (event) => {
            if (event.data.type === 'vadConfig') {
                this.vadContext = { ...this.vadContext, ...event.data.config };
            }
        };
    }

    detectVoiceActivity(inputData) {
        // Calculate energy (RMS) of the audio frame
        let rms = 0;
        let zeroCrossings = 0;
        
        // Calculate zero crossing rate
        let sign = Math.sign(inputData[0]);
        for (let i = 1; i < inputData.length; i++) {
            const nextSign = Math.sign(inputData[i]);
            if (sign !== nextSign && nextSign !== 0) {
                zeroCrossings++;
            }
            sign = nextSign;
            rms += inputData[i] * inputData[i];
        }
        
        rms = Math.sqrt(rms / inputData.length);
        const zcr = zeroCrossings / (inputData.length - 1);
        
        // Apply smoothing to the signal
        if (this.vadContext.history.length > 0) {
            rms = this.vadContext.smoothingTimeConstant * this.vadContext.history[this.vadContext.history.length - 1].rms + 
                 (1 - this.vadContext.smoothingTimeConstant) * rms;
            this.vadContext.zeroCrossingRate = this.vadContext.smoothingTimeConstant * this.vadContext.history[this.vadContext.history.length - 1].zcr +
                 (1 - this.vadContext.smoothingTimeConstant) * zcr;
        }
        
        // Keep history for smoothing
        this.vadContext.history.push({rms, zcr});
        if (this.vadContext.history.length > 10) {
            this.vadContext.history.shift();
        }
        
        // Multi-feature voice detection (energy + zero crossing rate)
        const isVoiceEnergy = rms > this.vadContext.voiceTrigger;
        const isVoiceZCR = this.vadContext.zeroCrossingRate > 0.1 && this.vadContext.zeroCrossingRate < 0.5;
        const isVoice = isVoiceEnergy && isVoiceZCR;
        
        // State tracking with minimum consecutive frames
        if (isVoice) {
            this.vadContext.consecutiveVoiceFrames++;
            this.silenceFrames = 0;
            return this.vadContext.consecutiveVoiceFrames >= this.vadContext.minVoiceFrames;
        } else {
            this.vadContext.consecutiveVoiceFrames = 0;
            // Only count as silence if below the lower threshold
            if (rms < this.vadContext.voiceStop) {
                this.silenceFrames++;
            }
            return this.silenceFrames < this.maxSilenceFrames;
        }
    }

    process(inputs, outputs, parameters) {
        const input = inputs[0];
        if (!input || !input[0]) return true;

        const inputChannel = input[0];
        const hasVoice = this.detectVoiceActivity(inputChannel);
        
        if (hasVoice) {
            if (!this.isStreaming) {
                this.isStreaming = true;
                this.port.postMessage({ type: 'voiceStart' });
            }
            
            for (let i = 0; i < inputChannel.length; i++) {
                this.buffer[this.bufferIndex++] = inputChannel[i];
                
                if (this.bufferIndex >= this.bufferSize) {
                    const int16Array = this.float32ToInt16(this.buffer);
                    this.port.postMessage({ type: 'audioData', data: int16Array });
                    this.bufferIndex = 0;
                }
            }
        } else if (this.isStreaming) {
            this.isStreaming = false;
            if (this.bufferIndex > 0) {
                const finalBuffer = this.buffer.slice(0, this.bufferIndex);
                const int16Array = this.float32ToInt16(finalBuffer);
                this.port.postMessage({ type: 'audioData', data: int16Array });
            }
            this.port.postMessage({ type: 'voiceEnd' });
            this.bufferIndex = 0;
        }

        return true;
    }

    float32ToInt16(float32Array) {
        const int16Array = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const s = Math.max(-1, Math.min(1, float32Array[i]));
            int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        return int16Array;
    }
}

registerProcessor('audio-processor', AudioProcessor);