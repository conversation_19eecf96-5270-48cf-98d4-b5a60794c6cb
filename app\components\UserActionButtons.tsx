"use client";

import { useRouter } from "next/navigation";
import { useAuth } from "../context/ApiAuthContext";
import { useState, useRef, useEffect } from "react";
import { setUserStage } from "../services/api";
import { UserStage } from "./UserJourneyPage";

interface UserActionButtonsProps {
  lng: string;
  variant?: "default" | "white";
  currentStage?: UserStage;
}

export default function UserActionButtons({ lng, variant = "default", currentStage }: UserActionButtonsProps) {
  const router = useRouter();
  const { logout } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isRestarting, setIsRestarting] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);



  // Handle user menu toggle
  const handleUserMenuToggle = () => {
    setShowUserMenu(!showUserMenu);
  };

  // Handle user menu item clicks
  const handleMenuItemClick = (item: string) => {
    setShowUserMenu(false);

    // Navigate or show functionality based on menu item
    switch (item) {
      case 'profile':
        // Navigate to dashboard page (profile/dashboard)
        router.push(`/${lng}/dashboard`);
        break;
      case 'security':
        // Navigate to change password page
        router.push(`/${lng}/change-password`);
        break;
      case 'contact':
        // Navigate to contact page
        router.push(`/${lng}/contact`);
        break;
      case 'terms':
        // Navigate to terms of use website
        window.open('https://www.lumalife.de', '_blank');
        break;
      case 'privacy':
        // Navigate to privacy page
        router.push(`/${lng}/privacy`);
        break;
      default:
        break;
    }
  };

  // Handle logout - use PPCA safe logout if on CONVERSATION stage
  const handleLogout = async () => {
    if (isLoggingOut) return; // Prevent multiple clicks

    setIsLoggingOut(true);
    try {
      // Check if we're on PPCA conversation screen
      if (currentStage === UserStage.CONVERSATION) {
        // Look for PPCAchat's logout function in the global scope
        if (typeof window !== 'undefined' && (window as any).ppcaSafeLogout) {
          await (window as any).ppcaSafeLogout();
        } else {
          // Fallback to regular logout if PPCA logout not available
          await logout();
        }
      } else {
        // Regular logout for other screens
        await logout();
      }
    } catch (err) {
      console.error("Error during logout:", err);
      // Force logout even if API call fails - clear ALL localStorage items
      localStorage.clear(); // This removes everything from localStorage
      window.location.href = `/${lng}/login`;
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Handle restart journey
  const handleResetJourney = async () => {
    if (isRestarting) return; // Prevent multiple clicks

    setIsRestarting(true);

    try {
      // Try to set user stage to 1 (WELCOME) using the proper API function
      try {
        // First, try to set the user stage to 1 (WELCOME)
        await setUserStage(1);
      } catch (error) {
        // Continue with logout even if this fails
      }

      // Clear ALL user data from localStorage
      localStorage.clear();

      // Redirect to login page
      window.location.href = `/${lng}/login`;
    } catch (error) {
      // Ensure we still redirect even if there's an error
      window.location.href = `/${lng}/login`;
    }
  };

  // Icon-only button styles for Help and User Menu (made bigger)
  const iconButtonClasses = "p-3 rounded-full text-gray-500 hover:text-white hover:bg-[#52bcc3] hover:shadow-md transition-all duration-300 transform hover:scale-105 active:scale-95";

  // Text button styles for Restart
  const buttonBaseClasses = "px-4 py-2 rounded-md text-sm font-medium transition-all border shadow-sm";
  const restartButtonClasses = `${buttonBaseClasses} bg-white text-gray-800 border-gray-300 hover:bg-gray-50 hover:border-gray-400 active:bg-gray-100`;

  return (
    <div className="fixed top-4 right-4 flex space-x-3 z-50">
      {/* User Menu Button */}
      <div className="relative" ref={userMenuRef}>
        <button
          onClick={handleUserMenuToggle}
          className={iconButtonClasses}
          title={lng === "de" ? "Benutzermenü" : "User Menu"}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
          </svg>
        </button>

        {/* User Menu Dropdown */}
        {showUserMenu && (
          <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
            <div className="py-2">
              {/* Profile */}
              <button
                onClick={() => handleMenuItemClick('profile')}
                className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center space-x-3 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>{lng === "de" ? "Profil" : "Profile"}</span>
              </button>

              {/* Security */}
              <button
                onClick={() => handleMenuItemClick('security')}
                className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center space-x-3 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                </svg>
                <span>{lng === "de" ? "Sicherheit" : "Security"}</span>
              </button>

              {/* Contact */}
              <button
                onClick={() => handleMenuItemClick('contact')}
                className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center space-x-3 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                </svg>
                <span>{lng === "de" ? "Kontakt" : "Contact"}</span>
              </button>

              {/* Terms of use */}
              <button
                onClick={() => handleMenuItemClick('terms')}
                className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center space-x-3 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                </svg>
                <span>{lng === "de" ? "Nutzungsbedingungen" : "Terms of use"}</span>
              </button>

              {/* Data privacy */}
              <button
                onClick={() => handleMenuItemClick('privacy')}
                className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center space-x-3 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-gray-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                </svg>
                <span>{lng === "de" ? "Datenschutzrichtlinien" : "Data privacy"}</span>
              </button>

              {/* Divider */}
              <div className="border-t border-gray-200 my-2"></div>

              {/* Logout - moved to end with red styling */}
              <button
                onClick={handleLogout}
                disabled={isLoggingOut}
                className="w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 flex items-center space-x-3 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-red-500">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                </svg>
                <span>
                  {isLoggingOut
                    ? (lng === "de" ? "Abmelden..." : "Logging out...")
                    : (lng === "de" ? "Abmelden" : "Logout")}
                </span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Restart Button */}
      <button
        onClick={handleResetJourney}
        className={`${restartButtonClasses} ${isRestarting ? 'opacity-70 cursor-wait' : ''} hidden`}
        disabled={isRestarting}
        title={lng === "de" ? "Reise neu starten" : "Restart Journey"}
      >
        {isRestarting
          ? (lng === "de" ? "Wird neu gestartet..." : "Restarting...")
          : (lng === "de" ? "Neustart" : "Restart")}
      </button>


    </div>
  );
}
