import { createInstance } from 'i18next';
import resourcesToBackend from 'i18next-resources-to-backend';
import { initReactI18next } from 'react-i18next/initReactI18next';
import { getOptions } from './settings';
import enCommon from '../../public/locales/en/common.json';
import deCommon from '../../public/locales/de/common.json';

// Pre-load translations to ensure server/client consistency
const resources = {
  en: { common: enCommon },
  de: { common: deCommon }
};

// Initialize i18next instance for server-side
const initI18next = async (lng: string, ns: string) => {
  const i18nInstance = createInstance();
  await i18nInstance
    .use(initReactI18next)
    .use(resourcesToBackend((language: string, namespace: string) =>
      import(`../../public/locales/${language}/${namespace}.json`)
    ))
    .init({
      ...getOptions(lng, ns),
      resources, // Include pre-loaded resources
      lng,
      ns,
      fallbackLng: 'en',
      preload: ['en', 'de']
    });
  return i18nInstance;
};

// Get translation function for server components
export async function getTranslation(lng: string, ns: string, options = {}) {
  const i18nextInstance = await initI18next(lng, ns);
  return {
    t: i18nextInstance.getFixedT(lng, ns, options),
    i18n: i18nextInstance
  };
}
