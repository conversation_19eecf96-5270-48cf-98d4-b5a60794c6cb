"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import PageTransition from "./PageTransition";
import { getTranslation } from "../i18n/translations";
import { STORAGE_KEYS } from "../config/appConfig";
import { useAuth } from "../context/ApiAuthContext";

// Client component that receives the lng parameter directly
export default function SignUpPage({ lng }: { lng: string }) {
  const router = useRouter();
  const [error, setError] = useState("");
  const [isRedirecting, setIsRedirecting] = useState(false);
  const { isAuthenticated } = useAuth();

  // Use the t function from our static translations
  const t = (key: string) => getTranslation(key, lng);

  // Immediate client-side check for token before component fully mounts
  useEffect(() => {
    // Skip if we're already redirecting
    if (isRedirecting) return;

    // Immediately check for token in localStorage
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);

    if (token) {
      console.log('Token found on initial load, redirecting immediately from signup page');
      // Don't wait for auth context, redirect immediately
      setIsRedirecting(true);

      // Use window.location for a hard redirect to avoid Next.js router issues
      window.location.href = `/${lng}/user-journey`;
    }
  }, []); // Empty dependency array means this runs once on mount

  // Secondary check that uses the auth context
  useEffect(() => {
    // Skip if we're already redirecting
    if (isRedirecting) return;

    // If authenticated, redirect to user journey
    if (isAuthenticated) {
      setIsRedirecting(true);
      window.location.href = `/${lng}/user-journey`;
    }
  }, [isAuthenticated, lng, isRedirecting]);
  function handleSignUp() {
    // Skip if we're already redirecting
    if (isRedirecting) return;

    // BYPASS: Skip phone verification and go directly to user details page
    // Simply redirect to phone verification page
    // Use window.location for a hard redirect to avoid Next.js router issues
    setIsRedirecting(true);
    // window.location.href = `/${lng}/phone-verification`;
    window.location.href = `/${lng}/user-details`;
  }

  return (
    <PageTransition>
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-white">
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-md p-6 sm:p-8 md:p-10 space-y-6 sm:space-y-8 bg-white border border-gray-200 rounded-2xl sm:rounded-3xl shadow-md">
          <div className="text-center">
            <div className="flex justify-end mb-2">
              <LanguageSwitcher currentLanguage={lng} />
            </div>
            <div className="flex justify-center mb-4 sm:mb-6">
              <img
                src="/images/logo_full.png"
                alt="Lumalife"
                className="h-10 sm:h-12 md:h-14 w-auto rounded-xl overflow-hidden"
              />
            </div>

            <p className="mb-6 sm:mb-8 text-gray-800 px-2 sm:px-4 text-base sm:text-lg">
              {t("sign_up_welcome")}
            </p>

            <h2 className="text-xl sm:text-2xl font-medium text-gray-800 mb-8 sm:mb-10 px-2 sm:px-4">
              {t("sign_up_title")}
            </h2>

            {error && (
              <div className="p-3 sm:p-4 mb-4 sm:mb-6 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 rounded-md text-red-500 text-xs sm:text-sm">
                {error}
              </div>
            )}

            <button
              onClick={handleSignUp}
              className="w-3/4 sm:w-2/3 md:w-1/2 py-2 px-4 sm:px-6 bg-[#52bcc3] text-white text-base sm:text-lg rounded-full font-medium hover:bg-[#3da8af] transition-colors mb-3"
            >
              {t("sign_up_button")}
            </button>

            <div className="text-center text-sm sm:text-base text-gray-700">
              {t("have_account")} <Link href={`/${lng}/login`} className="text-[#52bcc3] font-medium hover:underline">
                {t("login_button")}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
