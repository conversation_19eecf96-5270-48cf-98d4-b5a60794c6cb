"use client";

import { useRouter, usePathname } from "next/navigation";
import { memo } from "react";
import { languages } from "../i18n/settings";
import { updateUserLanguage } from "../services/api";
import { useAuth } from "../context/ApiAuthContext";

// Memoized language switcher to prevent unnecessary re-renders
const LanguageSwitcher = memo(function LanguageSwitcher({
  currentLanguage
}: {
  currentLanguage: string
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, refreshUserProfile } = useAuth();

  // Optimized language change handler
  const handleLanguageChange = async (newLang: string) => {
    if (newLang === currentLanguage) return;

    // Use regex for more reliable path extraction
    const pathWithoutLang = pathname.replace(/^\/[a-z]{2}(?:\/|$)/, '/');

    // If user is authenticated, update their language preference in the backend
    if (isAuthenticated) {
      try {
        // Update language in the backend and wait for it to complete
        await updateUserLanguage(newLang);

        // Force refresh the user profile to get the updated language
        try {
          await refreshUserProfile();
        } catch (refreshError) {
          console.error('LanguageSwitcher: Failed to refresh user profile after language update:', refreshError);
        }
      } catch (error) {
        console.error('LanguageSwitcher: Failed to update user language preference:', error);
        // Continue with navigation even if the API call fails
      }
    }

    // Navigate to the same page with new language
    router.push(`/${newLang}${pathWithoutLang === '/' ? '' : pathWithoutLang}`);
  };

  return (
    <select
      value={currentLanguage}
      onChange={(e) => handleLanguageChange(e.target.value)}
      className="bg-white border border-gray-300 rounded-md px-2 py-1.5 text-sm font-medium shadow-sm hover:border-gray-400 focus:outline-none focus:ring-1 focus:ring-[#52bcc3] focus:border-transparent"
      aria-label="Select language"
      style={{ color: '#333' }}
    >
      {languages.map(lang => (
        <option key={lang} value={lang} className="text-gray-800 font-medium">
          {lang.toUpperCase()}
        </option>
      ))}
    </select>
  );
});

export default LanguageSwitcher;
