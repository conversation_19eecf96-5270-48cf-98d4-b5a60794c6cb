import VerifyPage from "../../components/VerifyPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Verify Phone - Lumalife",
    description: "Verify your phone number"
  };
}

// Server component that passes the lng parameter to the client component
export default async function Verify({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <VerifyPage lng={lng} />;
}

