// Utility functions for error handling

/**
 * Extracts a user-friendly error message from various error types
 * @param error The error object
 * @param defaultMessage Default message to return if error can't be parsed
 * @returns A user-friendly error message
 */
export function getErrorMessage(error: unknown, defaultMessage = 'An unexpected error occurred'): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
    return error.message;
  }
  
  return defaultMessage;
}

/**
 * Logs an error to the console in development mode
 * @param error The error object
 * @param context Optional context information
 */
export function logError(error: unknown, context?: string): void {
  if (process.env.NODE_ENV !== 'production') {
    console.error(`Error${context ? ` in ${context}` : ''}:`, error);
  }
}
