"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import LanguageSwitcher from "./LanguageSwitcher";
import { getTranslation } from "../i18n/translations";
import { changePassword } from "../services/api";
import { useAuth } from "../context/ApiAuthContext";

export default function ChangePasswordPage({ lng }: { lng: string }) {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  const router = useRouter();
  const { isAuthenticated, logout, isLoading } = useAuth();

  // Helper function to get translation
  const t = (key: string) => getTranslation(key, lng);

  // Handle authentication check in useEffect to avoid render-time navigation
  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push(`/${lng}/login`);
      } else {
        setIsCheckingAuth(false);
      }
    }
  }, [isAuthenticated, isLoading, router, lng]);

  // Show loading while checking authentication
  if (isLoading || isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-gradient-to-b from-white to-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#52bcc3] mx-auto mb-4"></div>
          <div className="text-gray-600 text-lg">
            {lng === "de" ? "Wird geladen..." : "Loading..."}
          </div>
        </div>
      </div>
    );
  }

  // Don't render the form if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Function to check password strength - simplified to only check minimum length
  const checkPasswordStrength = (password: string) => {
    let strength = 0;

    // Check length - minimum 8 characters
    if (password.length >= 8) {
      strength = 4; // Set to maximum strength if minimum length is met
    } else {
      strength = Math.floor((password.length / 8) * 4);
    }

    setPasswordStrength(strength);
  };

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setNewPassword(password);
    checkPasswordStrength(password);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords
    if (!currentPassword.trim()) {
      setError(t("current_password_required") || "Current password is required");
      return;
    }

    if (!newPassword.trim()) {
      setError(t("password_required") || "New password is required");
      return;
    }

    if (newPassword.length < 8) {
      setError(t("password_too_short") || "Password must be at least 8 characters long");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError(t("passwords_do_not_match") || "Passwords do not match");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Call the API to change the password
      await changePassword(currentPassword, newPassword);
      setSuccess(true);

      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        router.push(`/${lng}/dashboard`);
      }, 3000);
    } catch (err) {

      // Handle different error types
      if (err && typeof err === 'object') {
        const apiError = err as any;

        // Handle authentication errors (401)
        if (apiError.status === 401 || apiError.status_code === 401) {
          setError(lng === "de"
            ? "Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an."
            : "Your session has expired. Please log in again.");

          // Redirect to login after showing error
          setTimeout(() => {
            logout();
            router.push(`/${lng}/login`);
          }, 2000);
          return;
        }

        // Handle incorrect current password (400)
        if (apiError.status === 400 || apiError.status_code === 400) {
          if (apiError.detail && apiError.detail.includes("Current password is incorrect")) {
            setError(lng === "de"
              ? "Das aktuelle Passwort ist falsch"
              : "Current password is incorrect");
          } else {
            setError(apiError.detail || (lng === "de"
              ? "Ungültige Anfrage. Bitte überprüfen Sie Ihre Eingaben."
              : "Invalid request. Please check your input."));
          }
          return;
        }

        // Handle server errors (500)
        if (apiError.status === 500 || apiError.status_code === 500) {
          setError(lng === "de"
            ? "Serverfehler. Bitte versuchen Sie es später erneut."
            : "Server error. Please try again later.");
          return;
        }

        // Handle other API errors with detail message
        if (apiError.detail) {
          setError(apiError.detail);
          return;
        }
      }

      // Handle Error instances
      if (err instanceof Error) {
        // Handle specific error messages
        if (err.message.includes("Current password is incorrect")) {
          setError(lng === "de" ? "Das aktuelle Passwort ist falsch" : "Current password is incorrect");
        } else if (err.message.includes("session") || err.message.includes("token")) {
          setError(lng === "de"
            ? "Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an."
            : "Your session has expired. Please log in again.");

          // Redirect to login after showing error
          setTimeout(() => {
            logout();
            router.push(`/${lng}/login`);
          }, 2000);
        } else if (err.message.includes("Network") || err.message.includes("fetch")) {
          setError(lng === "de"
            ? "Netzwerkfehler. Bitte überprüfen Sie Ihre Internetverbindung."
            : "Network error. Please check your internet connection.");
        } else {
          setError(err.message || t("error_generic") || "An error occurred. Please try again.");
        }
      } else {
        // Fallback for unknown error types
        setError(t("error_generic") || (lng === "de"
          ? "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut."
          : "An unexpected error occurred. Please try again."));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-gradient-to-b from-white to-gray-50">
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md p-6 sm:p-8 space-y-4 sm:space-y-6 bg-white border border-gray-200 rounded-2xl sm:rounded-3xl shadow-lg">
        <div className="text-center">
          <div className="flex justify-end mb-2">
            <LanguageSwitcher currentLanguage={lng} />
          </div>
          <div className="flex justify-center mb-3 sm:mb-4">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-8 sm:h-10 w-auto rounded-xl overflow-hidden"
            />
          </div>

          <h2 className="text-lg sm:text-xl font-semibold text-gray-800 mb-3 sm:mb-4">
            {lng === "de" ? "Passwort ändern" : "Change Password"}
          </h2>

          {error && (
            <div className={`border rounded-lg p-4 text-sm mb-4 ${
              error.includes("session") || error.includes("expired") || error.includes("Sitzung")
                ? "bg-yellow-50 border-yellow-200 text-yellow-800"
                : error.includes("Network") || error.includes("Netzwerk") || error.includes("Server") || error.includes("timeout")
                ? "bg-orange-50 border-orange-200 text-orange-800"
                : "bg-red-50 border-red-200 text-red-700"
            }`}>
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3 mt-0.5">
                  {error.includes("session") || error.includes("expired") || error.includes("Sitzung") ? (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  ) : error.includes("Network") || error.includes("Netzwerk") || error.includes("Server") || error.includes("timeout") ? (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="flex-1">
                  {error}
                </div>
              </div>
            </div>
          )}

          {!success ? (
            <>
              <p className="text-sm text-gray-600 mb-4 sm:mb-6">
                {lng === "de"
                  ? "Geben Sie Ihr aktuelles Passwort und Ihr neues Passwort ein."
                  : "Enter your current password and your new password."}
              </p>

              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5">
                <div className="relative">
                  <label className="block text-left text-sm font-medium text-gray-700 mb-1">
                    {lng === "de" ? "Aktuelles Passwort" : "Current Password"}
                  </label>
                  <input
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    placeholder={lng === "de" ? "Aktuelles Passwort eingeben" : "Enter current password"}
                    className="w-full px-4 py-3 border border-gray-300 rounded-full text-sm sm:text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#52bcc3] focus:border-transparent transition-all"
                    required
                  />
                </div>

                <div className="relative">
                  <label className="block text-left text-sm font-medium text-gray-700 mb-1">
                    {lng === "de" ? "Neues Passwort" : "New Password"}
                  </label>
                  <input
                    type="password"
                    value={newPassword}
                    onChange={handleNewPasswordChange}
                    placeholder={lng === "de" ? "Neues Passwort eingeben" : "Enter new password"}
                    className="w-full px-4 py-3 border border-gray-300 rounded-full text-sm sm:text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#52bcc3] focus:border-transparent transition-all"
                    required
                  />

                  {/* Password strength indicator */}
                  <div className="mt-2 h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className={`h-full transition-all duration-300 ${
                        passwordStrength === 0 ? 'w-0' :
                        passwordStrength === 1 ? 'w-1/4 bg-red-500' :
                        passwordStrength === 2 ? 'w-2/4 bg-orange-500' :
                        passwordStrength === 3 ? 'w-3/4 bg-yellow-500' :
                        'w-full bg-green-500'
                      }`}
                    ></div>
                  </div>
                  <div className="flex justify-between mt-1.5">
                    <p className="text-xs text-gray-500">
                      {lng === "de" ? "Mindestens 8 Zeichen" : "Minimum 8 characters"}
                    </p>
                    <p className="text-xs font-medium"
                      style={{
                        color: passwordStrength === 0 ? '#9CA3AF' :
                               passwordStrength === 1 ? '#EF4444' :
                               passwordStrength === 2 ? '#F97316' :
                               passwordStrength === 3 ? '#EAB308' :
                               '#22C55E'
                      }}
                    >
                      {passwordStrength === 0 ? (lng === "de" ? "Keine Eingabe" : "No input") :
                       passwordStrength === 1 ? (lng === "de" ? "Schwach" : "Weak") :
                       passwordStrength === 2 ? (lng === "de" ? "Mittel" : "Medium") :
                       passwordStrength === 3 ? (lng === "de" ? "Gut" : "Good") :
                       (lng === "de" ? "Stark" : "Strong")}
                    </p>
                  </div>
                </div>

                <div className="relative">
                  <label className="block text-left text-sm font-medium text-gray-700 mb-1">
                    {lng === "de" ? "Passwort bestätigen" : "Confirm Password"}
                  </label>
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder={lng === "de" ? "Passwort bestätigen" : "Confirm your password"}
                    className={`w-full px-4 py-3 border rounded-full text-sm sm:text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#52bcc3] focus:border-transparent transition-all ${
                      confirmPassword && newPassword && confirmPassword !== newPassword
                        ? 'border-red-300 bg-red-50'
                        : 'border-gray-300'
                    }`}
                    required
                  />
                  {confirmPassword && newPassword && confirmPassword !== newPassword && (
                    <p className="mt-1 text-xs text-red-500 text-left">
                      {lng === "de" ? "Passwörter stimmen nicht überein" : "Passwords don't match"}
                    </p>
                  )}
                </div>

                <div className="pt-1 sm:pt-2">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full py-3 sm:py-3.5 px-4 sm:px-6 text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 ${
                      isSubmitting
                        ? 'bg-gray-400 cursor-not-allowed opacity-70'
                        : 'bg-[#52bcc3] hover:bg-[#3da8af] hover:shadow-md'
                    }`}
                  >
                    {isSubmitting
                      ? (lng === "de" ? "Wird gespeichert..." : "Saving...")
                      : (lng === "de" ? "Passwort ändern" : "Change Password")}
                  </button>
                </div>
              </form>
            </>
          ) : (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-700 shadow-sm">
              <div className="flex items-center justify-center mb-2">
                <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-base font-medium mb-1">
                {lng === "de" ? "Passwort erfolgreich geändert!" : "Password Changed Successfully!"}
              </h3>
              <p className="text-sm">
                {lng === "de"
                  ? "Sie werden zum Dashboard weitergeleitet..."
                  : "Redirecting to dashboard..."}
              </p>
            </div>
          )}

          <div className="text-center text-xs sm:text-sm text-gray-700 mt-4 sm:mt-5">
            <Link href={`/${lng}/dashboard`} className="text-[#52bcc3] hover:underline font-medium">
              {lng === "de" ? "Zurück zum Dashboard" : "Back to Dashboard"}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
