class PlaybackWorklet extends AudioWorkletProcessor {
  constructor() {
    super();
    this.buffer = [];
    this.port.onmessage = this.handleMessage.bind(this);
  }

  handleMessage(event) {
    if (event.data === null) {
      this.buffer = [];
      return;
    }
    this.buffer.push(...event.data);
  }

  process(inputs, outputs, parameters) {
    const output = outputs[0];
    const channel = output[0];

    if (this.buffer.length > channel.length) {
      const toProcess = this.buffer.splice(0, channel.length);
      channel.set(toProcess.map((v) => v / 32768));
    } else {
      channel.fill(0);
    }

    return true;
  }
}

registerProcessor("playback-worklet", PlaybackWorklet);
