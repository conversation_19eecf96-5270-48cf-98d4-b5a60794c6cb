process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable React strict mode in development for faster renders
  reactStrictMode: false,

  // Enable compression for better performance
  compress: true,

  // Optimize images automatically
  images: {
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 300,
    unoptimized: true,
    remotePatterns: [],
  },

  // Improve performance by disabling x-powered-by header
  poweredByHeader: false,

  // Optimize for production
  productionBrowserSourceMaps: false,

  // Reduce TypeScript checking in development for faster refresh
  typescript: {
    ignoreBuildErrors: true,
  },

  // Reduce ESLint checking in development for faster refresh
  eslint: {
    ignoreDuringBuilds: true,
    dirs: [],
  },

  // Optimize for faster page loads
  experimental: {
    // Enable optimizations for faster page loads
    optimizeCss: true,
    scrollRestoration: true,
    // Improve memory usage
    memoryBasedWorkersCount: true,
    // Optimize package imports
    optimizePackageImports: ['voice-activity-detection'],
    // Enable Turbopack for faster development
    turbo: true,
  },

  // Optimize for faster dynamic routes
  onDemandEntries: {
    maxInactiveAge: 10 * 1000,
    pagesBufferLength: 1,
  },

  // Configure static file serving
  async rewrites() {
    return [
      {
        source: '/static/:path*',
        destination: '/static/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
