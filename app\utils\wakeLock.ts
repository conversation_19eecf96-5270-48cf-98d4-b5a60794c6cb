/**
 * Wake Lock Utility for preventing screen auto-lock on mobile and tablet devices
 * This utility provides a global wake lock system that can be used across all assistant screens
 */

// Global wake lock state
let wakeLock: WakeLockSentinel | null = null;
let isWakeLockActive = false;
let wakeLockRequestCount = 0;

/**
 * Check if the device is mobile or tablet
 */
function isMobileOrTablet(): boolean {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Tablet/i.test(navigator.userAgent);
}

/**
 * Check if Wake Lock API is supported
 */
function isWakeLockSupported(): boolean {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }
  return 'wakeLock' in navigator;
}

/**
 * Request wake lock to prevent screen from turning off
 * Uses reference counting to handle multiple components requesting wake lock
 * @param componentName - Name of the component requesting wake lock (for debugging)
 * @returns Promise<boolean> - True if wake lock was successfully acquired
 */
export async function requestWakeLock(componentName: string = 'Unknown'): Promise<boolean> {
  // Only proceed on mobile/tablet devices
  if (!isMobileOrTablet()) {
    console.log(`Wake lock not needed on desktop (requested by ${componentName})`);
    return false;
  }

  // Check if Wake Lock API is supported
  if (!isWakeLockSupported()) {
    console.warn(`Wake Lock API not supported on this device (requested by ${componentName})`);
    return false;
  }

  try {
    // Increment reference count
    wakeLockRequestCount++;
    console.log(`Wake lock requested by ${componentName} (count: ${wakeLockRequestCount})`);

    // If wake lock is already active, just return true
    if (isWakeLockActive && wakeLock) {
      console.log(`Wake lock already active (requested by ${componentName})`);
      return true;
    }

    // Request wake lock
    wakeLock = await navigator.wakeLock.request('screen');
    isWakeLockActive = true;

    console.log(`Wake lock acquired successfully by ${componentName}`);

    // Add event listener for wake lock release
    wakeLock.addEventListener('release', () => {
      console.log('Wake lock was released');
      isWakeLockActive = false;
      wakeLock = null;
    });

    return true;
  } catch (error) {
    console.error(`Failed to acquire wake lock for ${componentName}:`, error);
    isWakeLockActive = false;
    wakeLock = null;
    return false;
  }
}

/**
 * Release wake lock to allow screen to turn off
 * Uses reference counting to only release when all components are done
 * @param componentName - Name of the component releasing wake lock (for debugging)
 * @returns Promise<boolean> - True if wake lock was successfully released
 */
export async function releaseWakeLock(componentName: string = 'Unknown'): Promise<boolean> {
  // Decrement reference count
  if (wakeLockRequestCount > 0) {
    wakeLockRequestCount--;
  }

  console.log(`Wake lock release requested by ${componentName} (count: ${wakeLockRequestCount})`);

  // Only release if no more components need wake lock
  if (wakeLockRequestCount > 0) {
    console.log(`Wake lock still needed by other components (count: ${wakeLockRequestCount})`);
    return false;
  }

  // Release wake lock if it's active
  if (isWakeLockActive && wakeLock) {
    try {
      await wakeLock.release();
      console.log(`Wake lock released by ${componentName}`);
      isWakeLockActive = false;
      wakeLock = null;
      return true;
    } catch (error) {
      console.error(`Failed to release wake lock for ${componentName}:`, error);
      return false;
    }
  }

  console.log(`No active wake lock to release (requested by ${componentName})`);
  return false;
}

/**
 * Force release wake lock (emergency cleanup)
 * This bypasses reference counting and immediately releases the wake lock
 * @param reason - Reason for force release (for debugging)
 */
export async function forceReleaseWakeLock(reason: string = 'Unknown'): Promise<void> {
  console.log(`Force releasing wake lock: ${reason}`);
  
  wakeLockRequestCount = 0;
  
  if (isWakeLockActive && wakeLock) {
    try {
      await wakeLock.release();
      console.log(`Wake lock force released: ${reason}`);
    } catch (error) {
      console.error(`Failed to force release wake lock: ${reason}`, error);
    }
  }
  
  isWakeLockActive = false;
  wakeLock = null;
}

/**
 * Get current wake lock status
 * @returns Object with wake lock status information
 */
export function getWakeLockStatus(): {
  isSupported: boolean;
  isActive: boolean;
  isMobileDevice: boolean;
  requestCount: number;
} {
  return {
    isSupported: isWakeLockSupported(),
    isActive: isWakeLockActive,
    isMobileDevice: isMobileOrTablet(),
    requestCount: wakeLockRequestCount
  };
}

/**
 * Initialize wake lock cleanup listeners for page unload/visibility changes
 * This ensures wake lock is properly released when the page is closed or hidden
 */
function initializeWakeLockCleanup(): void {
  if (typeof window === 'undefined') return;

  // Handle page visibility changes
  const handleVisibilityChange = async () => {
    if (document.hidden) {
      // Page is hidden, release wake lock
      await forceReleaseWakeLock('Page hidden');
    } else if (!document.hidden && wakeLockRequestCount > 0) {
      // Page is visible again and components still need wake lock
      console.log('Page visible again, wake lock may need to be re-acquired by components');
    }
  };

  // Handle page unload
  const handleBeforeUnload = async () => {
    await forceReleaseWakeLock('Page unload');
  };

  // Handle page hide (mobile browsers)
  const handlePageHide = async () => {
    await forceReleaseWakeLock('Page hide');
  };

  // Add event listeners
  document.addEventListener('visibilitychange', handleVisibilityChange);
  window.addEventListener('beforeunload', handleBeforeUnload);
  window.addEventListener('pagehide', handlePageHide);

  console.log('Wake lock cleanup listeners initialized');
}

// Initialize cleanup listeners when module loads
if (typeof window !== 'undefined') {
  initializeWakeLockCleanup();
}
