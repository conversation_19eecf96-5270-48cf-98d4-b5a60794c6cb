import PhoneVerificationPage from "../../components/SignUpPagePhoneVerificationPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Phone Verification - Lumalife",
    description: "Verify your phone number to continue"
  };
}

// Server component that passes the lng parameter to the client component
export default async function PhoneVerification({
  params,
}: {
  params: { lng: string };
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <PhoneVerificationPage lng={lng} />;
}
