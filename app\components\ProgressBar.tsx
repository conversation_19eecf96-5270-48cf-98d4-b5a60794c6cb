"use client";

import { UserStage } from "./UserJourneyPage";
import { useState } from "react";

interface ProgressBarProps {
  currentStage: UserStage;
  feedbackStep?: "initial" | "start" | "recording";
  lng?: string;
}

export default function ProgressBar({ currentStage, feedbackStep = "initial", lng = "en" }: ProgressBarProps) {
  // feedbackStep is used by PageLayout but not directly in this component
  // Generate a unique ID for this instance of the progress bar
  // This ensures that SVG filters and gradients don't conflict when multiple progress bars are on the page
  const uniqueId = `progress-${Math.random().toString(36).substring(2, 9)}`;

  // State for tooltip visibility
  const [activeTooltip, setActiveTooltip] = useState<number | null>(null);

  // Function to handle mouse enter on milestone
  const handleMouseEnter = (index: number) => {
    setActiveTooltip(index);
  };

  // Function to handle mouse leave on milestone
  const handleMouseLeave = () => {
    setActiveTooltip(null);
  };

  // Common tooltip class for consistent styling
  const tooltipClass = "absolute bottom-[150%] left-1/2 transform -translate-x-1/2 bg-white px-1.5 py-0.5 xs:px-2 xs:py-1 sm:px-3 sm:py-2 rounded-lg shadow-lg text-[8px] xs:text-[10px] sm:text-xs md:text-sm text-gray-800 whitespace-normal min-w-[200px] xs:min-w-[250px] sm:min-w-[300px] md:min-w-[350px] z-[100] border border-[#52bcc3] text-center";

  // Common tooltip arrow class
  const tooltipArrowClass = "absolute bottom-[-6px] left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 xs:w-2 xs:h-2 bg-white rotate-45 border-b border-r border-[#52bcc3]";

  // Tooltip content based on language
  // These tooltips correspond to the four milestones in the progress bar
  // Milestone 1: Avatar Selection (Stage 2) - User is approved and awaiting onboarding
  // Milestone 2: PPCA Creation (Stage 4) - Onboarding completed, awaiting PPCA conversation
  // Milestone 3: Feedback (Stage 6) - Time limit reached, awaiting feedback
  // Milestone 4: Regular/Thank You (Stage 7) - Feedback provided, journey completed
  const tooltips = {
    en: [
      "Approved / Awaiting onboarding with Luma",
      "Onboarding completed / Awaiting conversation with personalised Luma",
      "Time limit reached / Awaiting feedback conversation",
      "Feedback provided / User journey completed"
    ],
    de: [
      "Zugelassen / Personalisierung von Luma vervollständigen",
      "Personalisierung abgeschlossen / Personalisiertes Gespräch mit Luma",
      "Zeitlimit erreicht / Feedback geben",
      "Feedback gegeben / Prototyp-Reise beendet"
    ]
  };

  // Don't show progress bar on stage 1 (Welcome)
  if (currentStage === UserStage.NEW) {
    return null;
  }

  // Calculate progress based on stage
  let progressPercentage = 0;
  let progressWidth = "0%";
  let starPosition = 0;
  let starLeftPosition = "0%";

  // Calculate progress based on stage
  switch (currentStage) {
    case UserStage.AVATAR_SELECTION: // Stage 2
      progressPercentage = 0;
      progressWidth = "0%";
      starPosition = 0; // First circle (0-based index)
      starLeftPosition = "4px"; // First milestone
      break;
    case UserStage.ONBOARDING: // Stage 3
      progressPercentage = 0;
      progressWidth = "0%";
      starPosition = 0; // First circle
      starLeftPosition = "4px"; // First milestone
      break;
    case UserStage.PPCA_CREATION: // Stage 4
      progressPercentage = 33.333333;
      progressWidth = "calc(33.333333% - 2px)";
      starPosition = 1; // Second circle
      starLeftPosition = "33.333333%"; // Second milestone
      break;
    case UserStage.CONVERSATION: // Stage 5
      progressPercentage = 33.333333;
      progressWidth = "calc(33.333333% - 2px)";
      starPosition = 1; // Second circle
      starLeftPosition = "33.333333%"; // Second milestone
      break;
    case UserStage.FEEDBACK: // Stage 6
      progressPercentage = 66.666667;
      progressWidth = "calc(66.666667% - 2px)";
      starPosition = 2; // Third circle
      starLeftPosition = "66.666667%"; // Third milestone
      break;
    case UserStage.REGULAR: // Stage 7
      progressPercentage = 100;
      progressWidth = "calc(100% - 4px)";
      starPosition = 3; // Fourth circle
      starLeftPosition = "calc(100% - 4px)"; // Fourth milestone
      break;
    default:
      progressPercentage = 0;
      progressWidth = "0%";
      starPosition = 0;
      starLeftPosition = "4px";
  }

  return (
    <>
      <style jsx global>{`
        @keyframes shimmer {
          0% {
            background-position: 200% 0%;
          }
          100% {
            background-position: 0% 0%;
          }
        }
        .progress-bar-line {
          background-size: 200% 100%;
          animation: shimmer 2s infinite linear;
        }

        .progress-bar-star {
          filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.7));
        }

        /* No custom star-wrapper class needed */
      `}</style>

      <div className="relative w-full xs:w-[90%] sm:w-4/5 md:w-3/4 lg:w-2/3 mx-auto h-[28px] xs:h-[30px] sm:h-[35px] md:h-[40px] flex items-center px-2 xs:px-3 sm:px-4 progress-bar-container">
        <div className="relative w-full h-[16px] xs:h-[18px] sm:h-[20px] md:h-[24px] flex items-center">
          {/* Progress bar */}
          <div className="h-1.5 xs:h-2 sm:h-2.5 md:h-3 bg-white border border-gray-300 rounded-full overflow-hidden shadow-inner w-[calc(100%-8px)] mx-auto">
            <div
              className="h-full bg-gradient-to-r from-[#3da8af] via-[#4ab5bd] to-[#52bcc3] relative progress-bar-line"
              style={{
                width: progressWidth,
                transition: 'width 1.5s cubic-bezier(0.25, 0.1, 0.25, 1)',
                willChange: 'width'
              }}
            >
          </div>
        </div>

        {/* Circles positioned to overlap the bar - these are the four milestones */}
        <div className="absolute top-1/2 left-0 w-full transform -translate-y-1/2">
          {/* Circle 1 - Avatar Selection (Stage 2) - "Approved / Awaiting onboarding with Luma" */}
          <div
            className={`w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 ${progressPercentage >= 0 ? 'bg-gradient-to-r from-[#52bcc3] to-[#3da8af]' : 'bg-white'}
            rounded-full border border-gray-300 z-10 shadow-md transition-all duration-700 cursor-pointer absolute
            ${starPosition === 0 ? 'ring-1 sm:ring-2 md:ring-3 ring-[#52bcc3]/70' : 'hover:ring-1 hover:ring-[#52bcc3]/30'}`}
            style={{ left: '4px', transform: 'translate(-50%, -50%)' }}
            onMouseEnter={() => handleMouseEnter(0)}
            onMouseLeave={handleMouseLeave}
            role="button"
            aria-label={tooltips[lng as keyof typeof tooltips][0]}
          >
            {/* Milestone 1 indicator */}

            {activeTooltip === 0 && (
              <div className={tooltipClass}>
                {tooltips[lng as keyof typeof tooltips][0]}
                <div className={tooltipArrowClass}></div>
              </div>
            )}
          </div>

          {/* Circle 2 - PPCA Creation (Stage 4) - "Onboarding completed / Awaiting conversation with personalised Luma" */}
          <div
            className={`w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 ${progressPercentage >= 33.333333 ? 'bg-gradient-to-r from-[#52bcc3] to-[#3da8af]' : 'bg-white'}
            rounded-full border border-gray-300 z-10 shadow-md transition-all duration-700 cursor-pointer absolute
            ${starPosition === 1 ? 'ring-1 sm:ring-2 md:ring-3 ring-[#52bcc3]/70' : 'hover:ring-1 hover:ring-[#52bcc3]/30'}`}
            style={{ left: '33.333333%', transform: 'translate(-50%, -50%)' }}
            onMouseEnter={() => handleMouseEnter(1)}
            onMouseLeave={handleMouseLeave}
            role="button"
            aria-label={tooltips[lng as keyof typeof tooltips][1]}
          >
            {/* Milestone 2 indicator */}

            {activeTooltip === 1 && (
              <div className={tooltipClass}>
                {tooltips[lng as keyof typeof tooltips][1]}
                <div className={tooltipArrowClass}></div>
              </div>
            )}
          </div>

          {/* Circle 3 - Feedback (Stage 6) - "Time limit reached / Awaiting feedback conversation" */}
          <div
            className={`w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 ${progressPercentage >= 66.666667 ? 'bg-gradient-to-r from-[#52bcc3] to-[#3da8af]' : 'bg-white'}
            rounded-full border border-gray-300 z-10 shadow-md transition-all duration-700 cursor-pointer absolute
            ${starPosition === 2 ? 'ring-1 sm:ring-2 md:ring-3 ring-[#52bcc3]/70' : 'hover:ring-1 hover:ring-[#52bcc3]/30'}`}
            style={{ left: '66.666667%', transform: 'translate(-50%, -50%)' }}
            onMouseEnter={() => handleMouseEnter(2)}
            onMouseLeave={handleMouseLeave}
            role="button"
            aria-label={tooltips[lng as keyof typeof tooltips][2]}
          >
            {/* Milestone 3 indicator */}

            {activeTooltip === 2 && (
              <div className={tooltipClass}>
                {tooltips[lng as keyof typeof tooltips][2]}
                <div className={tooltipArrowClass}></div>
              </div>
            )}
          </div>

          {/* Circle 4 - Regular/Thank You (Stage 7) - "Feedback provided / User journey completed" */}
          <div
            className={`w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 ${progressPercentage >= 100 ? 'bg-gradient-to-r from-[#52bcc3] to-[#3da8af]' : 'bg-white'}
            rounded-full border border-gray-300 z-10 shadow-md transition-all duration-700 cursor-pointer absolute
            ${starPosition === 3 ? 'ring-1 sm:ring-2 md:ring-3 ring-[#52bcc3]/70' : 'hover:ring-1 hover:ring-[#52bcc3]/30'}`}
            style={{ left: 'calc(100% - 4px)', transform: 'translate(-50%, -50%)' }}
            onMouseEnter={() => handleMouseEnter(3)}
            onMouseLeave={handleMouseLeave}
            role="button"
            aria-label={tooltips[lng as keyof typeof tooltips][3]}
          >
            {/* Milestone 4 indicator */}

            {activeTooltip === 3 && (
              <div className={tooltipClass}>
                {tooltips[lng as keyof typeof tooltips][3]}
                <div className={tooltipArrowClass}></div>
              </div>
            )}
          </div>
        </div>

        {/* Moving star that animates between positions */}
        <div
          className="absolute top-1/2 z-30 pointer-events-none"
          style={{
            left: starLeftPosition,
            transform: 'translate(-50%, -50%)',
            transition: 'left 1.5s cubic-bezier(0.25, 0.1, 0.25, 1)',
            willChange: 'left'
          }}
        >
          <div className="bg-white rounded-full w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 flex items-center justify-center shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="w-2.5 h-2.5 xs:w-3 xs:h-3 sm:w-3.5 sm:h-3.5 md:w-4 md:h-4 lg:w-5 lg:h-5 drop-shadow-lg progress-bar-star">
              <defs>
                <linearGradient id={`starGradient-${uniqueId}`} x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#FFD700" />
                  <stop offset="100%" stopColor="#FFA500" />
                </linearGradient>
              </defs>
              <path
                fillRule="evenodd"
                d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                clipRule="evenodd"
                fill={`url(#starGradient-${uniqueId})`}
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
