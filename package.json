{"name": "chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/copy-env-to-static.js && next dev --turbopack", "build": "node scripts/copy-env-to-static.js && next build", "start": "next start", "lint": "next lint", "copy-env": "node scripts/copy-env-to-static.js"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.13.5", "accept-language": "^3.0.20", "critters": "^0.0.24", "css-select": "^5.1.0", "dotenv": "^16.5.0", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.0.5", "i18next-resources-to-backend": "^1.2.1", "next": "15.3.1", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.1", "voice-activity-detection": "^0.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}