/**
 * Audio streaming utility for real-time voice communication
 * Handles WebSocket audio streaming in PCM16 format
 */

export class AudioManager {
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;
  private queue: AudioBuffer[] = [];
  private isPlaying: boolean = false;
  private crossfadeDuration: number = 0.015; // 15ms crossfade
  private bufferSize: number = 2048; // Smaller buffer for smoother playback
  private playbackNode: AudioWorkletNode | null = null;

  async init() {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 24000  // Match Azure's required sample rate
      });

      try {
        // Load the playback worklet
        await this.audioContext.audioWorklet.addModule('/playback-worklet.js');
        this.playbackNode = new AudioWorkletNode(this.audioContext, 'playback-worklet');
        this.playbackNode.connect(this.audioContext.destination);
      } catch (error) {
        console.error("Error loading audio worklet:", error);
        // Fallback to traditional gain node approach
        this.gainNode = this.audioContext.createGain();
        this.gainNode.connect(this.audioContext.destination);
      }
    }
  }

  async addToQueue(base64Data: string) {
    try {
      const binary = atob(base64Data);
      const bytes = new Uint8Array(binary.length);
      for (let i = 0; i < binary.length; i++) {
        bytes[i] = binary.charCodeAt(i);
      }

      const pcmData = new Int16Array(bytes.buffer);

      if (this.playbackNode) {
        // Use the worklet for playback
        this.playbackNode.port.postMessage(Array.from(pcmData));
      } else {
        // Process in smaller chunks for traditional approach
        for (let offset = 0; offset < pcmData.length; offset += this.bufferSize) {
          const chunk = pcmData.slice(offset, offset + this.bufferSize);
          const audioBuffer = await this.createAudioBuffer(chunk);
          if (audioBuffer) {
            this.queue.push(audioBuffer);
          }
        }

        if (!this.isPlaying) {
          this.playNext();
        }
      }
    } catch (error) {
      console.error("Error processing audio:", error);
    }
  }

  private async createAudioBuffer(pcmData: Int16Array) {
    if (!this.audioContext) return null;

    const audioBuffer = this.audioContext.createBuffer(1, pcmData.length, this.audioContext.sampleRate);
    const channelData = audioBuffer.getChannelData(0);

    // Convert Int16 to Float32 with smoother normalization
    for (let i = 0; i < pcmData.length; i++) {
      // Smooth normalization with slight compression
      channelData[i] = Math.max(-1, Math.min(1, (pcmData[i] / 32768) * 0.95));
    }

    return audioBuffer;
  }

  private scheduleBuffer(audioBuffer: AudioBuffer, startTime: number) {
    if (!this.audioContext || !this.gainNode || !audioBuffer) return 0;

    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;

    // Create individual gain node for this buffer
    const bufferGain = this.audioContext.createGain();
    bufferGain.connect(this.gainNode);

    // Implement crossfading
    const fadeIn = this.audioContext.currentTime + startTime;
    const fadeOut = fadeIn + (audioBuffer.duration - this.crossfadeDuration);

    // Fade in
    bufferGain.gain.setValueAtTime(0, fadeIn);
    bufferGain.gain.linearRampToValueAtTime(1, fadeIn + this.crossfadeDuration);

    // Fade out
    bufferGain.gain.setValueAtTime(1, fadeOut);
    bufferGain.gain.linearRampToValueAtTime(0, fadeOut + this.crossfadeDuration);

    source.connect(bufferGain);
    source.start(fadeIn);

    return audioBuffer.duration;
  }

  private async playNext() {
    if (this.queue.length === 0) {
      this.isPlaying = false;
      return;
    }

    this.isPlaying = true;
    const buffer = this.queue.shift();

    if (!buffer) {
      this.isPlaying = false;
      return;
    }

    // Schedule current buffer
    const duration = this.scheduleBuffer(buffer, 0);

    // Schedule next buffer with slight overlap for crossfading
    setTimeout(() => {
      this.playNext();
    }, Math.max(0, (duration - this.crossfadeDuration) * 1000));
  }

  clear() {
    this.queue = [];
    this.isPlaying = false;

    if (this.playbackNode) {
      // Clear the worklet buffer
      this.playbackNode.port.postMessage(null);
    } else if (this.gainNode && this.audioContext) {
      this.gainNode.gain.cancelScheduledValues(this.audioContext.currentTime);
      this.gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
      this.gainNode.gain.linearRampToValueAtTime(1, this.audioContext.currentTime + 0.01);
    }
  }

  resume() {
    this.audioContext?.resume();
  }
}

export class VoiceRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private stream: MediaStream | null = null;
  private isRecording: boolean = false;
  private onAudioAvailable: (base64Audio: string) => void;
  private audioContext: AudioContext | null = null;
  private audioWorkletNode: AudioWorkletNode | null = null;
  private scriptProcessor: ScriptProcessorNode | null = null;
  private mediaStreamSource: MediaStreamAudioSourceNode | null = null;

  constructor(onAudioAvailable: (base64Audio: string) => void) {
    this.onAudioAvailable = (base64Audio: string) => {
      // Add debug logging to check if audio data is being sent
      console.log(`Sending audio data: ${base64Audio.substring(0, 20)}... (${base64Audio.length} bytes)`);

      // Call the original callback
      onAudioAvailable(base64Audio);
    };
  }

  async init() {
    try {
      // Request microphone access with simpler constraints first
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: true
      });

      // Test if we're getting audio data
      const audioTracks = this.stream.getAudioTracks();
      if (audioTracks.length > 0) {
        // Check if the track is actually receiving data
        const audioTrack = audioTracks[0];
        if (audioTrack.muted || !audioTrack.enabled) {
          audioTrack.enabled = true;
        }
      } else {
        console.error('No audio tracks found in the stream');
      }

      // Create audio context with desired sample rate
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 24000           // Match Azure's required sample rate
      });

      console.log('Audio context created with sample rate:', this.audioContext.sampleRate);

      this.mediaStreamSource = this.audioContext.createMediaStreamSource(this.stream);

      try {
        // Try to use AudioWorklet (modern approach)
        await this.audioContext.audioWorklet.addModule('/audio-processor.js');
        this.audioWorkletNode = new AudioWorkletNode(this.audioContext, 'audio-processor');

        this.audioWorkletNode.port.onmessage = (event) => {
          if (!this.isRecording) return;

          const message = event.data;

          switch (message.type) {
            case 'voiceStart':
              break;

            case 'audioData':
              // Convert to base64
              const base64Audio = this.int16ArrayToBase64(message.data);

              // Send to server
              if (this.onAudioAvailable) {
                this.onAudioAvailable(base64Audio);
              }
              break;

            case 'voiceEnd':
              break;
          }
        };

        this.mediaStreamSource.connect(this.audioWorkletNode);
        this.audioWorkletNode.connect(this.audioContext.destination);

      } catch (error) {
        // AudioWorklet not supported, falling back to ScriptProcessor

        // Fallback to ScriptProcessor (older browsers)
        const bufferSize = 2048;
        this.scriptProcessor = this.audioContext.createScriptProcessor(bufferSize, 1, 1);

        this.scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
          if (!this.isRecording) return;

          const inputBuffer = audioProcessingEvent.inputBuffer;
          const inputData = inputBuffer.getChannelData(0);

          // Convert Float32Array to Int16Array
          const int16Data = new Int16Array(inputData.length);
          for (let i = 0; i < inputData.length; i++) {
            // Convert float32 to int16
            const s = Math.max(-1, Math.min(1, inputData[i]));
            int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
          }

          // Convert to base64
          const base64Audio = this.int16ArrayToBase64(int16Data);

          // Send to server
          if (this.onAudioAvailable) {
            this.onAudioAvailable(base64Audio);
          }
        };

        this.mediaStreamSource.connect(this.scriptProcessor);
        this.scriptProcessor.connect(this.audioContext.destination);
      }

    } catch (error) {
      console.error("Error initializing microphone:", error);
      throw error;
    }
  }

  private int16ArrayToBase64(int16Array: Int16Array): string {
    const uint8Array = new Uint8Array(int16Array.buffer);
    let binaryString = '';
    for (let i = 0; i < uint8Array.length; i++) {
      binaryString += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binaryString);
  }

  start() {
    if (!this.isRecording) {
      this.isRecording = true;
      this.audioContext?.resume();
    }
  }

  stop() {
    if (this.isRecording) {
      this.isRecording = false;
    }
  }

  cleanup() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
    }

    if (this.audioWorkletNode) {
      this.audioWorkletNode.disconnect();
      this.audioWorkletNode = null;
    }

    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect();
      this.scriptProcessor = null;
    }

    if (this.mediaStreamSource) {
      this.mediaStreamSource.disconnect();
      this.mediaStreamSource = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.stream = null;
  }
}
