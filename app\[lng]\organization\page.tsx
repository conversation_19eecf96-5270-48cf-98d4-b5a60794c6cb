import OrganizationPage from "../../components/OrganizationPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Organization - Lumalife",
    description: "Join through an organization"
  };
}

// Server component that passes the lng parameter to the client component
export default async function Organization({
  params,
}: {
  params: { lng: string };
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <OrganizationPage lng={lng} />;
}
