"use client";

import Link from "next/link";
import { useSession } from "next-auth/react";
import { useTranslation } from "../i18n/client";
import { useLanguage } from "../context/LanguageContext";
import { useAuth } from "../context/ApiAuthContext";
import LanguageSwitcher from "./LanguageSwitcher";

// Navbar with i18n support
export default function Navbar() {
  let session;
  let isSessionAvailable = false;
  try {
    // Defensive: next-auth/react may not be available during SSR/prerender
    const sessionHook = useSession?.();
    session = sessionHook?.data;
    isSessionAvailable = !!sessionHook;
  } catch {
    session = undefined;
    isSessionAvailable = false;
  }
  const { currentLanguage } = useLanguage();
  const { user, logout, isAuthenticated, isAdmin } = useAuth();

  // Use language from user profile if available, otherwise use the language from context
  const userLanguage = user?.details?.language || currentLanguage;
  const { t } = useTranslation("common", userLanguage);

  return (
    <nav className="border-b border-foreground/10 bg-background">
      <div className="max-w-7xl mx-auto px-3 sm:px-4">
        <div className="flex justify-between h-14 sm:h-16 items-center">
          <Link href={`/${userLanguage}`} className="font-bold text-lg sm:text-xl">
            {t("app_name")}
          </Link>

          <div className="flex items-center space-x-2 sm:space-x-4">
            <LanguageSwitcher currentLanguage={userLanguage} />

            {isAuthenticated || (isSessionAvailable && session) ? (
              // Logged in state
              <>
                {isAdmin && (
                  <Link
                    href={`/${userLanguage}/admin/dashboard`}
                    className="px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm rounded border border-foreground/20 hover:bg-foreground/5 text-[#52bcc3]"
                  >
                    {t("nav_admin_dashboard") || "Admin Dashboard"}
                  </Link>
                )}
                <button
                  onClick={async () => {
                    if (isAuthenticated) {
                      // Clean up audio connections before logout
                      if (typeof window !== 'undefined') {
                        try {
                          const audioUtils = await import('../utils/audioStreamingStatic');
                          audioUtils.emergencyLogoutCleanup();
                        } catch (cleanupErr) {
                          console.log('Error during audio cleanup:', cleanupErr);
                        }
                      }
                      logout();
                    }
                  }}
                  className="px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm rounded border border-foreground/20 hover:bg-foreground/5"
                >
                  {t("nav_sign_out")}
                </button>
              </>
            ) : (
              // Logged out state
              <>
                <Link
                  href={`/${userLanguage}/login`}
                  className="px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm rounded border border-foreground/20 hover:bg-foreground/5"
                >
                  {t("nav_sign_in")}
                </Link>
                <Link
                  href={`/${userLanguage}/signup`}
                  className="px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm rounded bg-foreground text-background hover:bg-foreground/90"
                >
                  {t("nav_sign_up")}
                </Link>
                <Link
                  href={`/${userLanguage}/login`}
                  className="px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm rounded border border-foreground/20 hover:bg-foreground/5 text-[#52bcc3]"
                >
                  {t("nav_admin") || "Admin"}
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
