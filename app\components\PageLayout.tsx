"use client";

import React, { ReactNode, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { UserStage } from "./UserJourneyPage";
import TopBar from "./TopBar";
import UserActionButtons from "./UserActionButtons";
import { useLanguage } from "../context/LanguageContext";
import { useAuth } from "../context/ApiAuthContext";

interface PageLayoutProps {
  children: ReactNode;
  // Make currentStage optional to support pages without progress bar
  currentStage?: UserStage;
  feedbackStep?: "initial" | "start" | "recording";
  // Add option to hide language switcher
  hideLanguageSwitcher?: boolean;
  // Add option to customize container style
  containerStyle?: "rounded" | "full" | "admin";
  // Add option to show/hide progress bar
  showProgressBar?: boolean;
  // Add option for custom header content
  customHeader?: ReactNode;
  // Add option to show/hide user action buttons (logout and restart)
  showActionButtons?: boolean;
  // Add option to customize action buttons variant
  actionButtonsVariant?: "default" | "white";
}

/**
 * PageLayout component that provides a consistent layout for all screens
 * Ensures the top portion (logo, progress bar, icons) stays in the same position
 */
export default function PageLayout({
  children,
  currentStage,
  feedbackStep,
  hideLanguageSwitcher = false,
  containerStyle = "rounded",
  showProgressBar = true,
  customHeader,
  showActionButtons = true,
  actionButtonsVariant = "default"
}: PageLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  // Get current language from context
  const { currentLanguage } = useLanguage();
  // Get authentication state and user profile
  const { isAuthenticated, user } = useAuth();

  // Use language from user profile if available, otherwise use the language from context
  const userLanguage = user?.details?.language || currentLanguage;

  // Sync URL language with API language for authenticated users
  useEffect(() => {
    if (isAuthenticated && user?.details?.language && user.details.language !== currentLanguage) {
      // Extract path without language prefix
      const pathWithoutLang = pathname.replace(/^\/[a-z]{2}(?:\/|$)/, '/');
      // Redirect to the correct language URL
      const newPath = `/${user.details.language}${pathWithoutLang === '/' ? '' : pathWithoutLang}`;
      router.replace(newPath);
    }
  }, [isAuthenticated, user?.details?.language, currentLanguage, pathname, router]);
  // Define responsive container classes based on style
  const containerClasses = {
    rounded: "w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-2xl bg-white border border-gray-200 rounded-[20px] sm:rounded-[30px] md:rounded-[40px] shadow-lg fixed-container",
    full: "w-full bg-white shadow-sm fixed-container",
    admin: "w-full bg-white shadow-sm fixed-container"
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center pt-4 sm:pt-5 md:pt-6 lg:pt-8 bg-gradient-to-b from-white to-[#f8fdfd]">
      {/* Show action buttons if user is authenticated and showActionButtons is true */}
      {isAuthenticated && showActionButtons && (
        <div className="fixed top-3 sm:top-4 md:top-5 right-3 sm:right-4 md:right-5 z-50">
          <UserActionButtons
            lng={userLanguage}
            variant={actionButtonsVariant}
            currentStage={currentStage}
          />
        </div>
      )}

      {/* Responsive width container with consistent styling */}
      <div className={containerClasses[containerStyle]}>
        <div className="flex flex-col h-full">
          {/* Top navigation bar with logo, progress bar and icons - responsive height */}
          {customHeader ? (
            // Use custom header if provided
            <div className="fixed-top-bar bg-white">
              {customHeader}
            </div>
          ) : (
            // Default header with logo, progress bar, and language switcher
            <TopBar
              currentStage={currentStage}
              feedbackStep={feedbackStep}
              hideLanguageSwitcher={hideLanguageSwitcher}
              showProgressBar={showProgressBar}
              userLanguage={userLanguage}
            />
          )}

          {/* Content area with dynamic height but consistent top margin */}
          <div className="content-area">
            {React.Children.map(children, child => {
              if (React.isValidElement(child)) {
                // Pass the API-determined language to child components
                return React.cloneElement(child, { lng: userLanguage } as any);
              }
              return child;
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
