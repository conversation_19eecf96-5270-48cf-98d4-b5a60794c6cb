import ChangePasswordPage from "../../components/ChangePasswordPage";
import { Metadata } from "next";

// Generate metadata for the page
export async function generateMetadata({
  params
}: {
  params: { lng: string }
}): Promise<Metadata> {
  return {
    title: "Change Password - Lumalife",
    description: "Change your password for Lumalife"
  };
}

// Server component that passes the lng parameter to the client component
export default async function ChangePassword({
  params,
}: {
  params: Promise<{ lng: string }>;
}) {
  // Await the params to properly access lng
  const { lng } = await params;

  return <ChangePasswordPage lng={lng} />;
}
