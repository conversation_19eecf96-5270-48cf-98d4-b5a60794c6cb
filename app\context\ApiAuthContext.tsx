"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { login, logout, getUserProfile, UserProfile, handleApiError } from "../services/api";
import { useLanguage } from "./LanguageContext";
import { getTranslation } from "../i18n/translations";

interface AuthContextType {
  user: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isApproved: boolean;
  isAdmin: boolean;
  login: (email: string, password: string) => Promise<boolean>; // Return true if login successful
  logout: () => Promise<void>;
  refreshUserProfile: () => Promise<UserProfile | null>; // Function to refresh the user profile
  error: string | null;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: false,
  isAuthenticated: false,
  isApproved: false,
  isAdmin: false,
  login: async () => false, // Return false by default
  logout: async () => {},
  refreshUserProfile: async () => null, // Return null by default
  error: null,
});

export function ApiAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { currentLanguage } = useLanguage();

  // Helper function to navigate to dashboard only if not already there
  // Currently unused but kept for future reference
  /*
  const navigateToDashboardIfNeeded = () => {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      if (!currentPath.includes('/dashboard')) {
        router.push(`/${currentLanguage}/dashboard`);
        return true; // Navigation occurred
      } else {
        console.log('Already on dashboard, staying on current page');
        return false; // No navigation needed
      }
    }
    return false;
  };
  */

  // Helper function to navigate based on user role and stage
  const navigateBasedOnUserStage = (userProfile: UserProfile) => {
    console.log('User profile loaded with role:', userProfile.role, 'and stage:', userProfile.current_stage);

    // First check if user is admin - if so, redirect to admin dashboard
    const isUserAdmin = userProfile.role === 'admin' ||
                       localStorage.getItem('userRole') === 'admin' ||
                       (userProfile.email && userProfile.email.toLowerCase().includes('admin'));

    if (isUserAdmin) {
      console.log('User is admin, should redirect to admin dashboard');
      // Store the role in localStorage for future reference
      localStorage.setItem('userRole', 'admin');

      // Check if we're already on the admin dashboard to prevent redirect loops
      if (typeof window !== 'undefined' && window.location.pathname.includes('/admin/dashboard')) {
        console.log('Already on admin dashboard, no need to redirect');
        return false;
      }

      // Return true to indicate that the user should be redirected to the admin dashboard
      return true;
    }

    // For regular users, we don't redirect based on user stage anymore
    // The UserJourneyPage component will handle showing the appropriate screen
    // based on the user's current_stage
    console.log('User is not admin, should proceed to user journey');
    return false;
  };

  // Check if user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setIsLoading(false);
        return;
      }

      try {
        const userProfile = await getUserProfile();

        // Even if the user is not approved, still set the user data
        // This allows the confirmation page to check approval status
        setUser(userProfile);

        // Check if user is admin and should be redirected
        const shouldRedirectToAdmin = navigateBasedOnUserStage(userProfile);

        // If user is admin, redirect to admin dashboard
        if (shouldRedirectToAdmin) {
          // Check if we're already on the admin dashboard to prevent redirect loops
          if (typeof window !== 'undefined' && !window.location.pathname.includes('/admin/dashboard')) {
            // Use window.location for a hard redirect to avoid Next.js router issues
            window.location.href = `/${currentLanguage}/admin/dashboard`;
            return;
          }
        }
      } catch (err) {
        // Only set error for non-auth errors (the auth errors will redirect)
        if (err instanceof Response && err.status === 401) {
          // Don't redirect to login page if we're on the confirmation page
          if (typeof window !== 'undefined' && window.location.pathname.includes('/confirmation')) {
            // Just clear the error and continue
            setError(null);
          }
        } else {
          // Other error - set error message
          const errorMessage = await handleApiError(err);
          setError(typeof errorMessage === 'string' ? errorMessage : errorMessage.detail);
        }
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router, currentLanguage]);

  const handleLogin = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    // Clear any previous user data
    setUser(null);

    try {
      // Step 1: Try to login with the API
      // Real API login
      try {
        // First attempt to get a token
        await login(email, password);

        // Step 2: If login successful, fetch the user profile
        try {
          const userProfile = await getUserProfile();

          // Step 3: Check if the user is approved
          // We still set the user data even if not approved
          // This allows the confirmation page to check approval status
          if (!userProfile.is_approved) {
            // Check if we're on the confirmation page
            if (typeof window !== 'undefined' && window.location.pathname.includes('/confirmation')) {
              // Set the user data anyway to allow approval status checking
              setUser(userProfile);
              setIsLoading(false);
              return true;
            } else {
              // For other pages, show the approval message
              const approvalErrorMsg = 'Your account is awaiting administrator approval.';
              setError(approvalErrorMsg);

              // Store the error in a temporary variable to ensure it's passed back to the login page
              localStorage.setItem('temp_auth_error', approvalErrorMsg);

              setUser(null);
              setIsLoading(false);
              return false;
            }
          }

          // User is approved, set the user data
          setUser(userProfile);
          setIsLoading(false);
          return true;

        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);

          // If we can't get the profile, assume the user is valid but log the error
          setIsLoading(false);
          return true;
        }
      } catch (loginError) {

        // Handle specific error types
        if (loginError && typeof loginError === 'object') {
          // Check if it's an invalid credentials error (401)
          if (
            ('status' in loginError && (loginError as any).status === 401) ||
            ('status_code' in loginError && (loginError as any).status_code === 401) ||
            ('error_type' in loginError && (loginError as any).error_type === 'INVALID_CREDENTIALS')
          ) {
            const invalidCredentialsMsg = 'Invalid email or password';
            setError(invalidCredentialsMsg);

            // Store the error in localStorage to ensure it's passed to the login page
            localStorage.setItem('temp_auth_error', invalidCredentialsMsg);
          } else if ('detail' in loginError) {
            const detailError = (loginError as any).detail;
            setError(detailError);

            // Store the error in localStorage
            localStorage.setItem('temp_auth_error', detailError);
          } else {
            const genericError = 'An error occurred during login. Please try again.';
            setError(genericError);

            // Store the error in localStorage
            localStorage.setItem('temp_auth_error', genericError);
          }
        } else if (loginError instanceof Error) {
          const errorMsg = loginError.message;
          setError(errorMsg);

          // Store the error in localStorage
          localStorage.setItem('temp_auth_error', errorMsg);
        } else {
          const genericError = 'An error occurred during login. Please try again.';
          setError(genericError);

          // Store the error in localStorage
          localStorage.setItem('temp_auth_error', genericError);
        }

        setIsLoading(false);
        return false;
      }
    } catch (err) {
      console.error('Unexpected error during login process:', err);
      const unexpectedError = 'An unexpected error occurred. Please try again.';
      setError(unexpectedError);

      // Store the error in localStorage
      localStorage.setItem('temp_auth_error', unexpectedError);

      setIsLoading(false);
      return false;
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Clean up audio connections before logout
      if (typeof window !== 'undefined') {
        try {
          const audioUtils = await import('../utils/audioStreamingStatic');
          audioUtils.emergencyLogoutCleanup();
        } catch (cleanupErr) {
          console.log('Error during audio cleanup:', cleanupErr);
        }
      }

      await logout();
      setUser(null);
      router.push(`/${currentLanguage}/login`);
    } catch (err) {
      const errorMessage = await handleApiError(err);
      setError(typeof errorMessage === 'string' ? errorMessage : errorMessage.detail);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to refresh the user profile
  const refreshUserProfile = async (): Promise<UserProfile | null> => {
    try {
      const userProfile = await getUserProfile();
      setUser(userProfile);
      return userProfile;
    } catch (err) {
      console.error('Error refreshing user profile:', err);
      return null;
    }
  };

  // Check if user is an admin - check user role, localStorage, and email
  const isAdmin = !!user && (
    user.role === 'admin' ||
    (typeof localStorage !== 'undefined' && localStorage.getItem('userRole') === 'admin') ||
    (user.email && user.email.toLowerCase().includes('admin'))
  );

  // Add a useEffect to periodically refresh user data on the confirmation page
  useEffect(() => {
    // Only run this on the confirmation page
    if (typeof window === 'undefined' || !window.location.pathname.includes('/confirmation')) {
      return;
    }

    // Function to refresh user data
    const refreshUserData = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        const userProfile = await getUserProfile();
        setUser(userProfile);
      } catch (error) {
        console.error('Error refreshing user data:', error);
      }
    };

    // Set up interval to refresh user data every 5 seconds
    const refreshInterval = setInterval(refreshUserData, 5000);

    // Clean up interval when component unmounts
    return () => {
      clearInterval(refreshInterval);
    };
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        isApproved: !!user?.is_approved,
        isAdmin: !!isAdmin,
        login: handleLogin,
        logout: handleLogout,
        refreshUserProfile,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}
