
name: CICD-frontend

on:
  push:
    branches: [dev]
  workflow_dispatch:  # Allow manual triggering

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source
        uses: actions/checkout@v3

      - name: Login to Docker Hub
        run: echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build Docker image
        run: |
          # Get short SHA of the commit
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)
          echo "Building Docker image with tag: $SHORT_SHA"

          # Get environment variables from the server's .env file
          if [ -f "$HOME/.env" ]; then
            echo "Using environment variables from server's .env file for build"
            # Extract API URL and WS URL from the .env file
            API_URL=$(grep NEXT_PUBLIC_API_URL $HOME/.env | cut -d '=' -f2)
            WS_URL=$(grep NEXT_PUBLIC_WS_URL $HOME/.env | cut -d '=' -f2)

            # Ensure WebSocket URL uses the correct protocol
            if [[ "$WS_URL" == http://* ]]; then
              echo "Converting WebSocket URL from http:// to ws://"
              WS_URL=${WS_URL/http:\/\//ws:\/\/}
            elif [[ "$WS_URL" == https://* ]]; then
              echo "Converting WebSocket URL from https:// to wss://"
              WS_URL=${WS_URL/https:\/\//wss:\/\/}
            fi

            echo "Using API URL: $API_URL"
            echo "Using WS URL: $WS_URL"
          else
            echo "WARNING: .env file not found on server, using default values"
            API_URL="https://api-dev.lumalife.de"
            WS_URL="wss://api-dev.lumalife.de"
          fi

          # Build the image with both latest and SHA tags
          echo "Building Docker image with environment variables..."
          docker build -t marcelk15/cicd-frontend-dev:latest -t marcelk15/cicd-frontend-dev:$SHORT_SHA \
            --build-arg DOCKER_BUILD=true \
            --build-arg NEXT_PUBLIC_API_URL="$API_URL" \
            --build-arg NEXT_PUBLIC_WS_URL="$WS_URL" \
            .

      - name: Publish image to Docker Hub
        run: |
          # Get short SHA of the commit
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)

          # Push both tags
          echo "Pushing Docker image with tags: latest, $SHORT_SHA"
          docker push marcelk15/cicd-frontend-dev:latest
          docker push marcelk15/cicd-frontend-dev:$SHORT_SHA

  deploy:
    needs: build
    runs-on: dev-frontend
    steps:
      - name: Login to Docker Hub
        run: echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin

      - name: Pull image from Docker Hub
        run: |
          # Get short SHA of the commit
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)

          # Pull both tags
          echo "Pulling Docker image with tags: latest, $SHORT_SHA"
          docker pull marcelk15/cicd-frontend-dev:latest
          docker pull marcelk15/cicd-frontend-dev:$SHORT_SHA || echo "Warning: Specific tag not found, using latest"

      - name: Copy and validate .env file
        run: |
          # Check if .env file exists in home directory
          if [ ! -f "$HOME/.env" ]; then
            echo "ERROR: .env file not found in $HOME directory"
            echo "Please create an .env file on the server with proper production URLs"
            exit 1
          else
            # Copy the .env file
            cp $HOME/.env ./.env
            echo "Copied .env file from $HOME to current directory"

            # Check for required environment variables
            if ! grep -q "NEXT_PUBLIC_API_URL" .env; then
              echo "ERROR: NEXT_PUBLIC_API_URL not found in .env file"
              echo "Please add NEXT_PUBLIC_API_URL to your .env file on the server"
              exit 1
            fi

            if ! grep -q "NEXT_PUBLIC_WS_URL" .env; then
              echo "ERROR: NEXT_PUBLIC_WS_URL not found in .env file"
              echo "Please add NEXT_PUBLIC_WS_URL to your .env file on the server"
              exit 1
            fi

            # Ensure WebSocket URL uses the correct protocol
            if grep -q "NEXT_PUBLIC_WS_URL=http://" .env; then
              echo "Converting WebSocket URL from http:// to ws://"
              sed -i 's/NEXT_PUBLIC_WS_URL=http:\/\//NEXT_PUBLIC_WS_URL=ws:\/\//g' .env
            fi

            if grep -q "NEXT_PUBLIC_WS_URL=https://" .env; then
              echo "Converting WebSocket URL from https:// to wss://"
              sed -i 's/NEXT_PUBLIC_WS_URL=https:\/\//NEXT_PUBLIC_WS_URL=wss:\/\//g' .env
            fi
          fi

          # Display .env file content without sensitive information
          echo "Environment file contents (excluding sensitive data):"
          grep -v "SECRET\|PASSWORD\|KEY" .env | cat -n
          echo "Environment file is ready for deployment"

      - name: List containers before deletion
        run: docker ps -a

      - name: Delete old container
        run: docker rm -f cicd-frontend-dev || true

      - name: List containers after deletion
        run: docker ps -a

      - name: Run Docker container
        run: |
          # Extract environment variables for logging (without sensitive data)
          API_URL=$(grep NEXT_PUBLIC_API_URL .env | cut -d '=' -f2)
          WS_URL=$(grep NEXT_PUBLIC_WS_URL .env | cut -d '=' -f2)

          echo "Deploying with the following configuration:"
          echo "API URL: $API_URL"
          echo "WebSocket URL: $WS_URL"

          # Get short SHA of the commit
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-7)

          # Check if the specific tag exists
          if docker image inspect marcelk15/cicd-frontend-dev:$SHORT_SHA &>/dev/null; then
            IMAGE_TAG=$SHORT_SHA
            echo "Using specific image tag: $IMAGE_TAG"
          else
            IMAGE_TAG=latest
            echo "Specific tag not found, using latest"
          fi

          # Run the container with environment variables
          echo "Starting container with environment variables from .env file..."
          docker run -d -p 3000:3000 \
            --name cicd-frontend-dev \
            --env-file ./.env \
            -e NODE_ENV=production \
            --restart unless-stopped \
            marcelk15/cicd-frontend-dev:$IMAGE_TAG

          echo "Container started successfully!"
          echo "The application should now be accessible at http://$(hostname -I | awk '{print $1}'):3000"

      - name: Remove dangling Docker images
        run: docker image prune -f

      - name: Deployment summary
        run: |
          echo "=== Deployment Summary ==="
          echo "Commit: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Deployed by: ${{ github.actor }}"
          echo "Deployment time: $(date)"
          echo "Container status:"
          docker ps | grep cicd-frontend-dev
          echo "=========================="
