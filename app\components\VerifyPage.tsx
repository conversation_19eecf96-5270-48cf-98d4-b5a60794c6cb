"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "../i18n/client";

// Client component that receives the lng parameter
export default function SignUpPage({ lng }: { lng: string }) {
  const router = useRouter();
  const { t } = useTranslation("common", lng);
  const [verificationCode, setVerificationCode] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  async function handleSubmit() {
    setError("");
    setIsLoading(true);

    // Basic validation
    if (!verificationCode.trim()) {
      setError(t("verification_code_error"));
      setIsLoading(false);
      return;
    }

    try {
      // Simulate successful verification (no actual API call)
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Redirect to dashboard or next step
      router.push(`/${lng}/dashboard`);
    } catch (err) {
      setError(t("error_generic"));
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 bg-white">
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md p-6 sm:p-8 md:p-10 space-y-6 sm:space-y-8 bg-white border border-gray-200 rounded-2xl sm:rounded-3xl shadow-md">
        <div className="text-center">
          <div className="flex justify-center mb-4 sm:mb-6 md:mb-8">
            <img
              src="/images/logo_full.png"
              alt="Lumalife"
              className="h-10 sm:h-12 md:h-14 w-auto rounded-xl overflow-hidden"
            />
          </div>

          <h2 className="text-xl sm:text-2xl font-medium text-gray-800 mb-4 sm:mb-6 md:mb-8 px-2 sm:px-4">
            {t("verify_title")}
          </h2>

          {error && (
            <div className="p-3 sm:p-4 mb-4 sm:mb-6 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 rounded-md text-red-500 text-xs sm:text-sm">
              {error}
            </div>
          )}

          <div className="px-2 sm:px-4 mb-6 sm:mb-8">
            <div className="space-y-3 sm:space-y-4">
              <p className="text-sm sm:text-base text-gray-700 text-left mb-3 sm:mb-4">
                {t("verify_info")}
              </p>

              <input
                type="text"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder={t("verification_code_placeholder")}
                className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-md text-sm sm:text-base"
                required
              />
            </div>
          </div>

          <button
            onClick={handleSubmit}
            disabled={isLoading}
            className="w-3/4 sm:w-2/3 md:w-1/2 py-2 px-4 sm:px-6 bg-[#52bcc3] text-white text-sm sm:text-base md:text-lg rounded-full font-medium hover:bg-[#3da8af] transition-colors mb-3"
          >
            {isLoading ? t("verifying") : t("verify_button")}
          </button>

          <div className="text-center text-xs sm:text-sm md:text-base text-gray-600 mt-3 sm:mt-4">
            {t("resend_code")} <button className="text-[#52bcc3] font-medium hover:underline">{t("resend_button")}</button>
          </div>
        </div>
      </div>
    </div>
  );
}
