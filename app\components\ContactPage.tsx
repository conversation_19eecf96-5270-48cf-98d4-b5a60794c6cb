"use client";

import { useRouter } from "next/navigation";
import { useLanguage } from "../context/LanguageContext";
import { useAuth } from "../context/ApiAuthContext";
import UserActionButtons from "./UserActionButtons";

export default function ContactPage() {
  const router = useRouter();
  const { currentLanguage } = useLanguage();
  const { user } = useAuth();

  // Use language from user profile if available, otherwise use the language from context
  const userLanguage = user?.details?.language || currentLanguage;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Header with UserActionButtons */}
      <div className="fixed top-0 left-0 right-0 bg-white shadow-lg border-b border-gray-100 z-40 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-3">
              <img src="/images/logo_icon.png" alt="Logo" className="h-10 w-10" />
              <div>
                <h1 className="text-xl font-bold text-gray-800">
                  {userLanguage === "de" ? "Kontakt" : "Contact"}
                </h1>
                <p className="text-sm text-gray-500">
                  {userLanguage === "de" ? "Kontaktieren Sie uns" : "Get in touch with us"}
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <UserActionButtons lng={userLanguage} variant="default" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pt-20">
        <div className="py-12">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header Section */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-[#3da8af] to-[#52bcc3] rounded-full mb-6 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-8 h-8 text-white">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-4">
                {userLanguage === "de" ? "Kontaktieren Sie uns" : "Contact Us"}
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                {userLanguage === "de"
                  ? "Wir sind hier, um Ihnen zu helfen. Kontaktieren Sie uns für Fragen oder Unterstützung."
                  : "We're here to help you. Contact us for any questions or support."
                }
              </p>
            </div>

            {/* Contact Information Card */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-8">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">
                  {userLanguage === "de" ? "Kontakt-Informationen" : "Contact Information"}
                </h2>
                
                {/* Email Section */}
                <div className="bg-gray-50 rounded-xl p-6 mb-6">
                  <div className="flex items-center justify-center mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 text-blue-600">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    {userLanguage === "de" ? "E-Mail" : "Email"}
                  </h3>
                  <a 
                    href="mailto:<EMAIL>"
                    className="text-2xl font-bold text-[#52bcc3] hover:text-[#3da8af] transition-colors duration-300"
                  >
                    <EMAIL>
                  </a>
                  <p className="text-gray-600 mt-2">
                    {userLanguage === "de"
                      ? "Senden Sie uns eine E-Mail für allgemeine Anfragen und Support."
                      : "Send us an email for general inquiries and support."
                    }
                  </p>
                </div>

                {/* Additional Information */}
                <div className="bg-gradient-to-r from-[#3da8af]/10 to-[#52bcc3]/10 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    {userLanguage === "de" ? "Antwortzeit" : "Response Time"}
                  </h3>
                  <p className="text-gray-700">
                    {userLanguage === "de"
                      ? "Wir bemühen uns, alle E-Mails innerhalb von 24 Stunden zu beantworten."
                      : "We strive to respond to all emails within 24 hours."
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Back Button */}
            <div className="text-center">
              <button
                onClick={() => router.back()}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#3da8af] to-[#52bcc3] text-white rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5 mr-2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                </svg>
                {userLanguage === "de" ? "Zurück" : "Back"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
